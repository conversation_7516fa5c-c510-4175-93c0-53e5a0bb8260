#include <a_samp>
#include <a_mysql>
#include <zcmd>
#include <sscanf2>
#include <easy-dialog>
#include <progress2>
#include <textdraw-streamer>

// Custom Colors
#define COLOR_KAKZAH 0xB0C4DEFF
#define COLOR_ADMIN 0xFF6347FF
#define COLOR_GREY 0x808080FF
#define COLOR_RED 0xFF0000FF
#define COLOR_GREEN 0x00FF00FF
#define COLOR_YELLOW 0xFFFF00FF
#define COLOR_WHITE 0xFFFFFFFF

// Special Actions (missing from a_players.inc)
#define SPECIAL_ACTION_PISSING 68

// Database configuration
#define MYSQL_HOST      "localhost"
#define MYSQL_USER      "root"
#define MYSQL_PASS      ""
#define MYSQL_DATABASE  "samp_server"

// Warns system
enum wInfo
{
    wID,
    wPlayerName[MAX_PLAYER_NAME],
    wType[16],
    wAdmin[32],
    wDate[32],
    wReason[128]
}
new WarnInfo[MAX_PLAYERS][50][wInfo]; // Max 50 warns per player
new PlayerWarnsCount[MAX_PLAYERS];

// Pagination variables
new PlayerWarnsPage[MAX_PLAYERS]; // Current page for warns
new PlayerWarnsTarget[MAX_PLAYERS][MAX_PLAYER_NAME]; // Target name for checkwarns
new PlayerAHelpPage[MAX_PLAYERS]; // Current page for ahelp

// easyDialog - No need for dialog IDs

// Dialog styles (removed redefinitions - using a_samp.inc defaults)
// DIALOG_STYLE_PASSWORD = 3, DIALOG_STYLE_LIST = 2, DIALOG_STYLE_INPUT = 1

// Body Part System Enums
enum
{
    BODY_PART_TORSO = 3,
    BODY_PART_GROIN = 4,
    BODY_PART_LEFT_ARM = 5,
    BODY_PART_RIGHT_ARM = 6,
    BODY_PART_LEFT_LEG = 7,
    BODY_PART_RIGHT_LEG = 8,
    BODY_PART_HEAD = 9
}

enum
{
    CONDITION_NORMAL = 0,
    CONDITION_DISLOCATED,
    CONDITION_BROKEN_BONE,
    CONDITION_BRUISE
}

// Custom Weapon System Constants
enum {
    WEAPON_SLOT_FIST = 0,
    WEAPON_SLOT_MELEE = 1,
    WEAPON_SLOT_PISTOL = 2,
    WEAPON_SLOT_SHOTGUN = 3,
    WEAPON_SLOT_SMG = 4,
    WEAPON_SLOT_ASSAULT = 5,
    WEAPON_SLOT_RIFLE = 6,
    WEAPON_SLOT_HEAVY = 7,
    WEAPON_SLOT_THROWN = 8,
    WEAPON_SLOT_SPECIAL = 9,
    WEAPON_SLOT_GIFT = 10,
    WEAPON_SLOT_DETONATOR = 11,
    WEAPON_SLOT_SPRAY = 12
}

// Default magazine sizes for weapons
new WeaponMagazineSize[47] = {
    0,   // 0 - Fist
    0,   // 1 - Brass Knuckles
    0,   // 2 - Golf Club
    0,   // 3 - Nightstick
    0,   // 4 - Knife
    0,   // 5 - Baseball Bat
    0,   // 6 - Shovel
    0,   // 7 - Pool Cue
    0,   // 8 - Katana
    0,   // 9 - Chainsaw
    0,   // 10 - Purple Dildo
    0,   // 11 - Dildo
    0,   // 12 - Vibrator
    0,   // 13 - Silver Vibrator
    0,   // 14 - Flowers
    0,   // 15 - Cane
    0,   // 16 - Grenade
    0,   // 17 - Tear Gas
    0,   // 18 - Molotov Cocktail
    0,   // 19 - Vehicle Missile
    0,   // 20 - Hydra Flare
    0,   // 21 - Jetpack
    17,  // 22 - Colt 45 (9mm)
    17,  // 23 - Silenced 9mm
    7,   // 24 - Desert Eagle
    30,  // 25 - Shotgun
    2,   // 26 - Sawnoff Shotgun
    7,   // 27 - Combat Shotgun
    50,  // 28 - Micro SMG (Uzi)
    30,  // 29 - MP5
    30,  // 30 - AK-47
    50,  // 31 - M4
    1,   // 32 - Tec-9
    7,   // 33 - Country Rifle
    1,   // 34 - Sniper Rifle
    1,   // 35 - RPG
    1,   // 36 - HS Rocket
    0,   // 37 - Flamethrower
    0,   // 38 - Minigun
    0,   // 39 - Satchel Charge
    0,   // 40 - Detonator
    0,   // 41 - Spraycan
    0,   // 42 - Fire Extinguisher
    0,   // 43 - Camera
    0,   // 44 - Night Vis Goggles
    0,   // 45 - Thermal Goggles
    0    // 46 - Parachute
};

// Illegal weapons (will be shown in red)
new IllegalWeapons[] = {
    16, 17, 18, // Grenades, Tear Gas, Molotov
    22, 23, 24, // Pistols
    25, 26, 27, // Shotguns
    28, 29, 30, 31, 32, // SMGs and Assault Rifles
    33, 34, // Rifles
    35, 36, 37, 38, 39 // Heavy weapons
};

// Red screen textdraw for death
new Text:RedScreen;

// Forward declarations
forward Float:GetDistanceBetweenPoints(Float:x1, Float:y1, Float:z1, Float:x2, Float:y2, Float:z2);
forward UpdatePlayerInteriorVW(playerid);

// HBE System Forward declarations
forward UpdateHBESystem(playerid);
forward UpdateHBEProgressBars(playerid);
forward OnPissTimerUpdate(playerid); // Completes pissing based on bladder level (5-60 seconds)

// Function to calculate pissing duration based on bladder level
stock GetPissingDuration(bladderLevel)
{
    // Formula: Lower bladder = longer duration
    // 100% bladder = 5 seconds, 0% bladder = 60 seconds
    // Linear interpolation: duration = 60 - (bladderLevel * 0.55)
    new duration = 60 - ((bladderLevel * 55) / 100);
    if(duration < 5) duration = 5;   // Minimum 5 seconds
    if(duration > 60) duration = 60; // Maximum 60 seconds
    return duration;
}

// Player data enum
enum pInfo
{
    pID,
    pName[MAX_PLAYER_NAME],
    pPassword[129],
    pLevel,
    pMoney,
    Float:pPosX,
    Float:pPosY,
    Float:pPosZ,
    bool:pLogged,
    pGender,        // 0 = Male, 1 = Female
    pOrigin[32],
    pBirthDay,
    pBirthMonth,
    pBirthYear,
    pSpawnLocation,
    pSkin,
    pPlayingTime,      // Total playing time in seconds
    pLoginTime,        // Login timestamp for current session
    pAFKTimer,         // Timer for AFK kick (1 minute)
    pLoginAttempts,    // Login attempts counter (max 5)
    pLastLogin,        // Last login timestamp
    pLoadStartTime,    // Timestamp when login process started (for load time calculation)
    Text3D:pAdoText,   // 3D text for /ado command
    pAdminLevel,       // Admin level (0-10)
    pAdminDuty,        // Admin duty status (0/1)
    pAdminName[32],    // Custom admin name
    Float:pAngle,      // Player angle/rotation
    pDutyStartTime,    // When admin went on duty (timestamp)
    pTotalDutyTime,    // Total admin duty time in seconds
    pOriginPage,       // Current page for origin selection
    pIsNewPlayer,      // Flag to check if player is newly registered
    pAdminListPage,    // Current page for admin list
    pSelectedAdminID,  // Selected admin ID for management
    Float:pHealth,     // Player health
    Float:pArmour,     // Player armour
    Float:pMaxHealth,  // Player maximum health
    pWarnCount,        // Player warn count (0-10)
    pInterior,         // Player interior
    pVirtualWorld,     // Player virtual world

    // Death system variables
    bool:pIsDead,      // Player death status
    pDeathTimer,       // Timer for death recovery (3 minutes)
    pShakeTimer,       // Timer for screen shake effect
    pAnimTimer,        // Timer for death animation loop
    Float:pDeathX,     // Death position X
    Float:pDeathY,     // Death position Y
    Float:pDeathZ,     // Death position Z
    Float:pDeathAngle, // Death angle

    // Jail system variables
    bool:pJailed,      // Player jail status
    pJailTime,         // Jail time remaining in seconds
    pJailTimer,        // Timer for jail countdown
    PlayerText:pJailTextDraw[4], // Player textdraws for jail time display

    // Time system variables
    PlayerText:pTimeTextDraw[2], // Player textdraws for time display [0=date, 1=time]

    // Ban system variables
    bool:pBanned,      // Player ban status
    pBanTime,          // Ban expiry timestamp (0 = permanent)
    pBanReason[128],   // Ban reason

    // Hunger, Bladder, Energy system variables
    pHunger,           // Player hunger level (0-100)
    pBladder,          // Player bladder level (0-100)
    pEnergy,           // Player energy level (0-100)
    pHBETimer,         // Timer for hunger/bladder/energy decrease
    bool:pIsPissing,   // Player pissing status
    pPissTimer,        // Timer for pissing animation
    pHBEMode,          // HBE display mode (0=Classic, 1=Simple)

    // Body Part Status System
    pBodyPartCondition[7],  // Body part conditions (0=Normal, 1=Dislocated, 2=Broken Bone, 3=Bruise)
    pBodyPartTimer,     // Timer for body part healing

    // Custom Weapon System
    pWeaponAmmo[13],    // Custom ammo for each weapon slot (0-12)
    pWeaponMagazine[13], // Magazine size for each weapon slot
    Text:pAmmoTD,       // Player textdraw for ammo display
    pCurrentWeapon,     // Current weapon ID
    bool:pAmmoTDShown,  // Is ammo textdraw shown

    // Inventory System
    pInventoryWeapons[13], // Weapon IDs in inventory (0 = no weapon)
    pInventoryAmmoUnits[13], // Ammo units for each weapon (1 unit = 100 ammo)
    pInventoryMoney,    // Money in inventory (separate from pMoney for display)
    pSelectedWeaponSlot // Currently selected weapon slot for actions

};

new PlayerInfo[MAX_PLAYERS][pInfo];
new MySQL:g_SQL;

// Hunger, Bladder, Energy system variables
new PlayerText:PlayerTD[MAX_PLAYERS][10]; // Increased for simple mode
new PlayerBar:PlayerProgressBar[MAX_PLAYERS][3];

// Global time and weather system variables
new gCurrentWeather; // Current weather ID (will be randomized on startup)
new gWeatherList[] = {0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20}; // Available weather IDs
new gLastWeatherChange = 0; // Track last weather change to prevent duplicates

// Spawn locations
new Float:gSpawnLocations[][4] = {
    {1685.7581, -2334.8354, 13.5469, 359.1578}, // Los Santos Airport
    {2624.2319, -2469.2676, 3.0000, 177.5912},  // Ocean Docks Harbor
    {1691.4346, -1930.7275, 13.5474, 359.6395}  // Verdant Bluffs Station
};

// Jail positions (OOC Jail)
new Float:JailPositions[][4] = {
    {-27.3287, 2321.2803, 24.3034, 0.2184},  // OOCJAIL 1
    {-18.3471, 2322.7488, 24.3034, 3.3517},  // OOCJAIL 2
    {-10.7939, 2329.1865, 24.3034, 89.8324}, // OOCJAIL 3
    {-10.2566, 2337.0598, 24.3034, 89.8325}  // OOCJAIL 4
};

// Origins list - All recognized countries in the world
new gOrigins[][32] = {
    // Asia
    "Afghanistan", "Armenia", "Azerbaijan", "Bahrain", "Bangladesh",
    "Bhutan", "Brunei", "Cambodia", "China", "Cyprus",
    "Georgia", "India", "Indonesia", "Iran", "Iraq",
    "Israel", "Japan", "Jordan", "Kazakhstan", "Kuwait",
    "Kyrgyzstan", "Laos", "Lebanon", "Malaysia", "Maldives",
    "Mongolia", "Myanmar", "Nepal", "North Korea", "Oman",
    "Pakistan", "Palestine", "Philippines", "Qatar", "Russia",
    "Saudi Arabia", "Singapore", "South Korea", "Sri Lanka", "Syria",
    "Tajikistan", "Thailand", "Timor-Leste", "Turkey", "Turkmenistan",
    "United Arab Emirates", "Uzbekistan", "Vietnam", "Yemen",

    // Europe
    "Albania", "Andorra", "Austria", "Belarus", "Belgium",
    "Bosnia Herzegovina", "Bulgaria", "Croatia", "Czech Republic", "Denmark",
    "Estonia", "Finland", "France", "Germany", "Greece",
    "Hungary", "Iceland", "Ireland", "Italy", "Kosovo",
    "Latvia", "Liechtenstein", "Lithuania", "Luxembourg", "Malta",
    "Moldova", "Monaco", "Montenegro", "Netherlands", "North Macedonia",
    "Norway", "Poland", "Portugal", "Romania", "San Marino",
    "Serbia", "Slovakia", "Slovenia", "Spain", "Sweden",
    "Switzerland", "Ukraine", "United Kingdom", "Vatican City",

    // Africa
    "Algeria", "Angola", "Benin", "Botswana", "Burkina Faso",
    "Burundi", "Cameroon", "Cape Verde", "Central African Rep", "Chad",
    "Comoros", "DR Congo", "Congo", "Djibouti", "Egypt",
    "Equatorial Guinea", "Eritrea", "Eswatini", "Ethiopia", "Gabon",
    "Gambia", "Ghana", "Guinea", "Guinea-Bissau", "Ivory Coast",
    "Kenya", "Lesotho", "Liberia", "Libya", "Madagascar",
    "Malawi", "Mali", "Mauritania", "Mauritius", "Morocco",
    "Mozambique", "Namibia", "Niger", "Nigeria", "Rwanda",
    "Sao Tome Principe", "Senegal", "Seychelles", "Sierra Leone", "Somalia",
    "South Africa", "South Sudan", "Sudan", "Tanzania", "Togo",
    "Tunisia", "Uganda", "Zambia", "Zimbabwe",

    // North America
    "Antigua Barbuda", "Bahamas", "Barbados", "Belize", "Canada",
    "Costa Rica", "Cuba", "Dominica", "Dominican Republic", "El Salvador",
    "Grenada", "Guatemala", "Haiti", "Honduras", "Jamaica",
    "Mexico", "Nicaragua", "Panama", "Saint Kitts Nevis", "Saint Lucia",
    "Saint Vincent Grenadines", "Trinidad Tobago", "United States",

    // South America
    "Argentina", "Bolivia", "Brazil", "Chile", "Colombia",
    "Ecuador", "Guyana", "Paraguay", "Peru", "Suriname",
    "Uruguay", "Venezuela",

    // Oceania
    "Australia", "Fiji", "Kiribati", "Marshall Islands", "Micronesia",
    "Nauru", "New Zealand", "Palau", "Papua New Guinea", "Samoa",
    "Solomon Islands", "Tonga", "Tuvalu", "Vanuatu"
};

// Male skins (all valid male skins in SA-MP 0.3.7)
new gMaleSkins[] = {
    1, 2, 3, 4, 5, 6, 7, 8, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 32, 33, 34, 35, 36, 37, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 57, 58, 59, 60, 61, 62, 66, 67, 68, 70, 71, 72, 73, 78, 79, 80, 81, 82, 83, 84, 94, 95, 96, 97, 98, 99, 100, 101, 102, 103, 104, 105, 106, 107, 108, 109, 110, 111, 112, 113, 114, 115, 116, 117, 118, 120, 121, 122, 123, 124, 125, 126, 127, 128, 132, 133, 134, 135, 136, 137, 142, 143, 144, 146, 147, 153, 154, 155, 156, 158, 159, 160, 161, 162, 163, 164, 165, 166, 167, 168, 170, 171, 173, 174, 175, 176, 177, 179, 180, 181, 182, 183, 184, 185, 186, 187, 188, 189, 200, 202, 203, 204, 206, 208, 209, 210, 212, 213, 217, 220, 221, 222, 223, 227, 228, 229, 230, 234, 235, 236, 239, 240, 241, 242, 247, 248, 249, 250, 252, 253, 254, 255, 258, 259, 260, 261, 262, 264, 265, 266, 267, 268, 269, 270, 271, 272, 290, 291, 292, 293, 294, 295, 296, 297, 299
};

// Female skins (all valid female skins in SA-MP 0.3.7)
new gFemaleSkins[] = {
    9, 10, 11, 12, 13, 31, 38, 39, 40, 41, 53, 54, 55, 56, 63, 64, 65, 69, 75, 76, 77, 85, 87, 88, 89, 90, 91, 92, 93, 129, 130, 131, 138, 139, 140, 141, 145, 148, 150, 151, 152, 157, 169, 172, 178, 190, 191, 192, 193, 194, 195, 196, 197, 198, 199, 201, 205, 207, 211, 214, 215, 216, 218, 219, 224, 225, 226, 231, 232, 233, 237, 238, 243, 244, 245, 246, 251, 256, 257, 263, 298
};

// Removed unused dialog titles array - using string literals directly

// Removed unused dialog arrays - using string literals directly in ShowPlayerDialog calls

// Define camera modes at the top
#define CAMERA_MOVE  1
#define CAMERA_CUT   2

main()
{
    // Entry point wajib, walaupun kosong
}

// easyDialog handles all dialog functionality

// Dialog handlers using easyDialog
Dialog:AFKKick(playerid, response, listitem, inputtext[])
{
    // Player will be kicked anyway, no action needed
    return 1;
}

Dialog:RPNameError(playerid, response, listitem, inputtext[])
{
    // Player will be kicked anyway, no action needed
    return 1;
}

Dialog:Login(playerid, response, listitem, inputtext[])
{
    if(!response) return Kick(playerid);

    // Validate input is not empty
    if(strlen(inputtext) == 0)
    {
        new loginMsg[256];
        format(loginMsg, sizeof(loginMsg),
            "{FFFFFF}Selamat datang {00FF00}%s{FFFFFF}!\n\n\
            {FFFFFF}Kesempatan login: {FFFF00}(%d/5)\n\n\
            {FF0000}Password tidak boleh kosong!\n\
            {FFFFFF}Masukkan password Anda untuk login:",
            PlayerInfo[playerid][pName], PlayerInfo[playerid][pLoginAttempts]);

        Dialog_Show(playerid, Login, DIALOG_STYLE_PASSWORD, "Login", loginMsg, "Login", "Keluar");
        return 1;
    }

    // Increment login attempts
    PlayerInfo[playerid][pLoginAttempts]++;

    // Check password
    new hashedInput[129];
    HashPassword(inputtext, hashedInput, sizeof(hashedInput));

    if(!strcmp(PlayerInfo[playerid][pPassword], hashedInput, false))
    {
        // Password correct - load player data
        new query[256];
        mysql_format(g_SQL, query, sizeof(query), "SELECT * FROM `players` WHERE `username` = '%e'", PlayerInfo[playerid][pName]);
        mysql_tquery(g_SQL, query, "OnPlayerLogin", "i", playerid);
    }
    else
    {
        // Password wrong - check login attempts

        // Check login attempts
        if(PlayerInfo[playerid][pLoginAttempts] >= 5)
        {
            // Kick player after 5 failed attempts
            Dialog_Show(playerid, LoginFailed, DIALOG_STYLE_MSGBOX, "Login Gagal",
                "{FF0000}Anda telah di-kick dari server!\n\n{FFFFFF}Alasan: {FFFF00}Terlalu banyak percobaan login yang salah\n{FFFFFF}Maksimal: {FFFF00}5 kali percobaan\n\n{FFFFFF}Silakan connect kembali dan pastikan password benar.",
                "OK", "");

            new funcname[] = "KickPlayerDelayed";
            new formatstr[] = "i";
            SetTimerEx(funcname, 3000, false, formatstr, playerid);
        }
        else
        {
            // Show login dialog again with error message
            new loginMsg[256];
            format(loginMsg, sizeof(loginMsg),
                "{FFFFFF}Selamat datang {00FF00}%s{FFFFFF}!\n\n\
                {FFFFFF}Kesempatan login: {FFFF00}(%d/5)\n\n\
                {FF0000}Password salah! Coba lagi.\n\
                {FFFFFF}Masukkan password Anda untuk login:",
                PlayerInfo[playerid][pName], PlayerInfo[playerid][pLoginAttempts]);

            Dialog_Show(playerid, Login, DIALOG_STYLE_PASSWORD, "Login", loginMsg, "Login", "Keluar");
        }
    }
    return 1;
}

Dialog:LoginFailed(playerid, response, listitem, inputtext[])
{
    // Player will be kicked anyway, no action needed
    return 1;
}

Dialog:Register(playerid, response, listitem, inputtext[])
{
    if(!response) return Kick(playerid);

    if(strlen(inputtext) < 4)
    {
        Dialog_Show(playerid, Register, DIALOG_STYLE_PASSWORD, "Registrasi",
            "{FFFFFF}Selamat datang!\n\n{FF0000}Password minimal 4 karakter!\n{FFFF00}Masukkan password yang ingin Anda gunakan:",
            "Daftar", "Keluar");
        return 1;
    }

    // Hash and save password
    HashPassword(inputtext, PlayerInfo[playerid][pPassword], 129);

    // Show gender selection
    Dialog_Show(playerid, Gender, DIALOG_STYLE_LIST, "Pilih Gender", "Male\nFemale", "Pilih", "Kembali");
    return 1;
}

Dialog:Gender(playerid, response, listitem, inputtext[])
{
    if(!response) return Dialog_Show(playerid, Register, DIALOG_STYLE_PASSWORD, "Registrasi",
        "{FFFFFF}Selamat datang!\n\n{FFFF00}Akun Anda belum terdaftar.\nMasukkan password yang ingin Anda gunakan:",
        "Daftar", "Keluar");

    PlayerInfo[playerid][pGender] = listitem;

    // Set random skin based on gender and save it
    new skinid;
    if(listitem == 0) // Male
    {
        skinid = gMaleSkins[random(sizeof(gMaleSkins))];
    }
    else // Female
    {
        skinid = gFemaleSkins[random(sizeof(gFemaleSkins))];
    }
    PlayerInfo[playerid][pSkin] = skinid;
    SetPlayerSkin(playerid, skinid);

    // Show origin selection with pagination
    PlayerInfo[playerid][pOriginPage] = 0; // Start from first page
    ShowOriginDialog(playerid, 0);
    return 1;
}

Dialog:Origin(playerid, response, listitem, inputtext[])
{
    if(!response) return Dialog_Show(playerid, Gender, DIALOG_STYLE_LIST, "Pilih Gender", "Male\nFemale", "Pilih", "Kembali");

    new totalCountries = sizeof(gOrigins);
    new itemsPerPage = 10;
    new currentPage = PlayerInfo[playerid][pOriginPage];
    new startIndex = currentPage * itemsPerPage;
    new endIndex = startIndex + itemsPerPage;
    new countriesOnPage = (endIndex > totalCountries) ? (totalCountries - startIndex) : itemsPerPage;

    // Check if it's a navigation option
    new hasBack = (currentPage > 0);
    new hasNext = (endIndex < totalCountries);
    new navigationItems = 0;
    if(hasBack || hasNext) navigationItems = 1; // "--- Navigation ---" separator
    if(hasBack) navigationItems++;
    if(hasNext) navigationItems++;

    // Check if selected item is navigation
    if(listitem >= countriesOnPage + (navigationItems > 1 ? 1 : 0)) // After countries + separator
    {
        if(hasBack && listitem == countriesOnPage + 1) // "< Back" selected
        {
            PlayerInfo[playerid][pOriginPage]--;
            ShowOriginDialog(playerid, PlayerInfo[playerid][pOriginPage]);
            return 1;
        }
        else if(hasNext) // "Next >" selected
        {
            PlayerInfo[playerid][pOriginPage]++;
            ShowOriginDialog(playerid, PlayerInfo[playerid][pOriginPage]);
            return 1;
        }
    }
    else if(listitem < countriesOnPage) // Country selected
    {
        new selectedCountry = startIndex + listitem;
        format(PlayerInfo[playerid][pOrigin], 32, "%s", gOrigins[selectedCountry]);
        Dialog_Show(playerid, Birthday, DIALOG_STYLE_INPUT, "Masukkan Tanggal Lahir",
            "Masukkan tanggal lahir (DD/MM/YYYY):", "Lanjut", "Kembali");
        return 1;
    }

    // If something went wrong, show dialog again
    ShowOriginDialog(playerid, currentPage);
    return 1;
}

Dialog:Birthday(playerid, response, listitem, inputtext[])
{
    if(!response) return ShowOriginDialog(playerid, PlayerInfo[playerid][pOriginPage]);

    new day, month, year;
    if(sscanf(inputtext, "p</>ddd", day, month, year))
    {
        Dialog_Show(playerid, Birthday, DIALOG_STYLE_INPUT, "Masukkan Tanggal Lahir",
            "Format salah!\nMasukkan tanggal lahir (DD/MM/YYYY):", "Lanjut", "Kembali");
        return 1;
    }

    PlayerInfo[playerid][pBirthDay] = day;
    PlayerInfo[playerid][pBirthMonth] = month;
    PlayerInfo[playerid][pBirthYear] = year;

    // Show spawn location selection
    Dialog_Show(playerid, Spawn, DIALOG_STYLE_LIST, "Pilih Lokasi Spawn",
        "Los Santos Airport\nOcean Docks Harbor\nVerdant Bluffs Station", "Spawn", "Kembali");
    return 1;
}

Dialog:Spawn(playerid, response, listitem, inputtext[])
{
    if(!response) return Dialog_Show(playerid, Birthday, DIALOG_STYLE_INPUT, "Masukkan Tanggal Lahir",
        "Masukkan tanggal lahir (DD/MM/YYYY):", "Lanjut", "Kembali");

    PlayerInfo[playerid][pSpawnLocation] = listitem;

    // Finally register the account
    new query[1024];
    mysql_format(g_SQL, query, sizeof(query),
        "INSERT INTO players (username,password,gender,origin,birth_day,birth_month,birth_year,spawn_location,skin,playing_time,last_login,admin_level,admin_name,angle,total_duty_time,health,armour,max_health,hunger,bladder,energy,hbe_mode,body_part_conditions) \
        VALUES ('%e','%e',%d,'%e',%d,%d,%d,%d,%d,%d,%d,%d,'%e',%f,%d,%f,%f,%f,%d,%d,%d,%d,'%e')",
        PlayerInfo[playerid][pName], PlayerInfo[playerid][pPassword],
        PlayerInfo[playerid][pGender], PlayerInfo[playerid][pOrigin],
        PlayerInfo[playerid][pBirthDay], PlayerInfo[playerid][pBirthMonth],
        PlayerInfo[playerid][pBirthYear], PlayerInfo[playerid][pSpawnLocation],
        PlayerInfo[playerid][pSkin], 0, gettime(), 0, "", 0.0, 0, 100.0, 0.0, 100.0, 100, 100, 100, 0, "0,0,0,0,0,0,0");

    mysql_tquery(g_SQL, query, "OnPlayerRegister", "i", playerid);
    return 1;
}

Dialog:StatsError(playerid, response, listitem, inputtext[])
{
    // No action needed, just close
    return 1;
}

Dialog:Stats(playerid, response, listitem, inputtext[])
{
    // No action needed, just close
    return 1;
}

Dialog:Help(playerid, response, listitem, inputtext[])
{
    if(!response) return 1;

    switch(listitem)
    {
        case 0: // General Commands
        {
            Dialog_Show(playerid, HelpGeneral, DIALOG_STYLE_MSGBOX, "General Commands",
                "{FFFFFF}Available General Commands:\n\n\
                {FFFF00}/stats {FFFFFF}- Tampilkan statistik karakter Anda\n\
                {FFFF00}/myhealth {FFFFFF}- Tampilkan status kesehatan (body parts + HBE)\n\
                {FFFF00}/levels {FFFFFF}- Tampilkan syarat naik level\n\
                {FFFF00}/mywarns [page] {FFFFFF}- Lihat punishment records Anda (10 per halaman)\n\
                {FFFF00}/help {FFFFFF}- Tampilkan menu bantuan ini",
                "OK", "Back");
        }
        case 1: // Roleplay Commands
        {
            Dialog_Show(playerid, HelpRP, DIALOG_STYLE_MSGBOX, "Roleplay Commands",
                "{FFFFFF}Available Roleplay Commands:\n\n\
                {FFFF00}/me [aksi] {FFFFFF}- Deskripsikan aksi karakter Anda\n\
                {FFFF00}/do [deskripsi] {FFFFFF}- Deskripsikan lingkungan/situasi\n\
                {FFFF00}/ame [teks] {FFFFFF}- Teks di atas kepala (5 detik)\n\
                {FFFF00}/ado [teks] {FFFFFF}- Buat teks 3D (gunakan /ado untuk hapus)\n\n\
                {FFFFFF}Chat Volume Commands:\n\
                {FFFF00}/l [teks] {FFFFFF}- Bicara pelan/lirih (jarak 5m)\n\
                {FFFF00}/s [teks] {FFFFFF}- Berteriak keras (jarak 25m)\n\
                {FFFF00}/w [id] [teks] {FFFFFF}- Berbisik ke player tertentu (jarak 3m)\n\
                {FFFF00}/b [teks] {FFFFFF}- Chat OOC local",
                "OK", "Back");
        }
    }
    return 1;
}

Dialog:HelpGeneral(playerid, response, listitem, inputtext[])
{
    if(!response) // Back button
    {
        Dialog_Show(playerid, Help, DIALOG_STYLE_LIST, "Menu Bantuan - Pilih Kategori",
            "General Commands\nRoleplay Commands", "Pilih", "Tutup");
    }
    return 1;
}

Dialog:HelpRP(playerid, response, listitem, inputtext[])
{
    if(!response) // Back button
    {
        Dialog_Show(playerid, Help, DIALOG_STYLE_LIST, "Menu Bantuan - Pilih Kategori",
            "General Commands\nRoleplay Commands", "Pilih", "Tutup");
    }
    return 1;
}

Dialog:Levels(playerid, response, listitem, inputtext[])
{
    // No action needed, just close
    return 1;
}

Dialog:AHelp(playerid, response, listitem, inputtext[])
{
    if(!response) return 1;

    // Show commands for selected level
    ShowAdminLevelCommands(playerid, listitem);
    return 1;
}

Dialog:AHelpLevel(playerid, response, listitem, inputtext[])
{
    if(!response)
    {
        // Go back to level selection
        new adminLevel = PlayerInfo[playerid][pAdminLevel];
        new levelList[512];

        strcat(levelList, "Level\tDescription\n");

        if(adminLevel >= 1)
            strcat(levelList, "Level 1\tBasic Admin Commands\n");
        if(adminLevel >= 2)
            strcat(levelList, "Level 2\tPlayer Management\n");
        if(adminLevel >= 5)
            strcat(levelList, "Level 5\tAdvanced Commands\n");
        if(adminLevel >= 8)
            strcat(levelList, "Level 8\tSenior Admin Commands\n");
        if(adminLevel >= 10)
            strcat(levelList, "Level 10\tHead Admin Commands");

        Dialog_Show(playerid, AHelp, DIALOG_STYLE_TABLIST_HEADERS, "Admin Help - Select Level",
            levelList, "Select", "Close");
    }
    else
    {
        // Handle pagination for level 1
        new adminLevel = PlayerInfo[playerid][pAdminLevel];

        // Find which level was selected
        new selectedLevel = 0;
        new currentIndex = 0;
        if(adminLevel >= 1 && currentIndex == 0) selectedLevel = 1;
        else if(adminLevel >= 2 && ++currentIndex == 0) selectedLevel = 2;
        else if(adminLevel >= 5 && ++currentIndex == 0) selectedLevel = 5;
        else if(adminLevel >= 8 && ++currentIndex == 0) selectedLevel = 8;
        else if(adminLevel >= 10 && ++currentIndex == 0) selectedLevel = 10;

        if(selectedLevel == 1)
        {
            // Toggle page for level 1
            new currentPage = PlayerAHelpPage[playerid];
            if(currentPage < 1) currentPage = 1;

            if(currentPage == 1)
            {
                PlayerAHelpPage[playerid] = 2;
            }
            else
            {
                PlayerAHelpPage[playerid] = 1;
            }

            // Show the updated page
            ShowAdminLevelCommands(playerid, 0); // Level 1 is always index 0
        }
    }
    return 1;
}

Dialog:ADutyTime(playerid, response, listitem, inputtext[])
{
    // No action needed, just close
    return 1;
}

Dialog:ListAdmin(playerid, response, listitem, inputtext[])
{
    if(!response) return 1;

    // Check if it's navigation
    if(listitem >= 10) // Navigation items are at the end
    {
        new query[256];
        mysql_format(g_SQL, query, sizeof(query), "SELECT username, admin_name, admin_level, total_duty_time, id FROM `players` WHERE `admin_level` > 0 ORDER BY `admin_level` DESC, `username` ASC");

        if(listitem == 10) // Back button
        {
            if(PlayerInfo[playerid][pAdminListPage] > 0)
                PlayerInfo[playerid][pAdminListPage]--;
        }
        else if(listitem == 11) // Next button
        {
            PlayerInfo[playerid][pAdminListPage]++;
        }

        mysql_tquery(g_SQL, query, "OnLoadAdminList", "i", playerid);
        return 1;
    }

    // Player selected an admin, load admin management
    new query[256];
    mysql_format(g_SQL, query, sizeof(query), "SELECT username, admin_name, admin_level, total_duty_time, id FROM `players` WHERE `admin_level` > 0 ORDER BY `admin_level` DESC, `username` ASC LIMIT %d, 1",
        (PlayerInfo[playerid][pAdminListPage] * 10) + listitem);
    mysql_tquery(g_SQL, query, "OnSelectAdminForManagement", "i", playerid);

    return 1;
}

Dialog:AdminManage(playerid, response, listitem, inputtext[])
{
    if(!response)
    {
        // Go back to admin list
        new query[256];
        mysql_format(g_SQL, query, sizeof(query), "SELECT username, admin_name, admin_level, total_duty_time, id FROM `players` WHERE `admin_level` > 0 ORDER BY `admin_level` DESC, `username` ASC");
        mysql_tquery(g_SQL, query, "OnLoadAdminList", "i", playerid);
        return 1;
    }

    if(listitem == 0) // Set admin level
    {
        Dialog_Show(playerid, AdminManageSetLevel, DIALOG_STYLE_INPUT, "Set Admin Level",
            "Masukkan admin level baru (0-10):\n\n{FFFF00}0 = Remove admin\n1-10 = Admin levels",
            "Set", "Back");
    }
    else if(listitem == 1) // Kick admin (remove admin level)
    {
        Dialog_Show(playerid, AdminManageKick, DIALOG_STYLE_MSGBOX, "Kick Admin",
            "Apakah Anda yakin ingin menghapus admin level player ini?",
            "Ya", "Tidak");
    }

    return 1;
}

Dialog:AdminManageSetLevel(playerid, response, listitem, inputtext[])
{
    if(!response)
    {
        // Show admin management menu again
        ShowAdminManagementMenu(playerid);
        return 1;
    }

    new level = strval(inputtext);
    if(level < 0 || level > 10)
    {
        SendClientMessage(playerid, 0xFF0000FF, "ERROR: Admin level harus 0-10!");
        Dialog_Show(playerid, AdminManageSetLevel, DIALOG_STYLE_INPUT, "Set Admin Level",
            "Masukkan admin level baru (0-10):\n\n{FFFF00}0 = Remove admin\n1-10 = Admin levels\n\n{FF0000}ERROR: Level harus 0-10!",
            "Set", "Back");
        return 1;
    }

    // Update admin level in database
    new query[256];
    mysql_format(g_SQL, query, sizeof(query), "UPDATE `players` SET `admin_level` = %d WHERE `id` = %d", level, PlayerInfo[playerid][pSelectedAdminID]);
    mysql_tquery(g_SQL, query, "OnAdminLevelUpdated", "ii", playerid, level);

    return 1;
}

Dialog:AdminManageKick(playerid, response, listitem, inputtext[])
{
    if(!response)
    {
        ShowAdminManagementMenu(playerid);
        return 1;
    }

    // Remove admin level (set to 0)
    new query[256];
    mysql_format(g_SQL, query, sizeof(query), "UPDATE `players` SET `admin_level` = 0 WHERE `id` = %d", PlayerInfo[playerid][pSelectedAdminID]);
    mysql_tquery(g_SQL, query, "OnAdminLevelUpdated", "ii", playerid, 0);

    return 1;
}

// Function to validate Roleplay Name
stock IsValidRoleplayName(const name[])
{
    new len = strlen(name);
    if(len < 3 || len > MAX_PLAYER_NAME-1) return 0;

    new underscoreCount = 0;
    new hasUnderscore = 0;

    for(new i = 0; i < len; i++)
    {
        if(name[i] == '_')
        {
            underscoreCount++;
            hasUnderscore = 1;
            // Check if underscore is at start, end, or consecutive
            if(i == 0 || i == len-1 || (i > 0 && name[i-1] == '_')) return 0;
        }
        else if(!((name[i] >= 'A' && name[i] <= 'Z') || (name[i] >= 'a' && name[i] <= 'z')))
        {
            return 0; // Only letters and underscore allowed
        }
    }

    // Must have exactly one underscore (Firstname_Lastname format)
    if(underscoreCount != 1 || !hasUnderscore) return 0;

    return 1;
}

// Function to format money with cents (100 game units = $1.00)
stock FormatMoney(money, output[], maxlength)
{
    new dollars = money / 100;
    new cents = money % 100;

    if(dollars < 1000)
    {
        format(output, maxlength, "$%d.%02d", dollars, cents);
    }
    else if(dollars < 1000000)
    {
        new thousands = dollars / 1000;
        new remainder = dollars % 1000;
        format(output, maxlength, "$%d,%03d.%02d", thousands, remainder, cents);
    }
    else if(dollars < 1000000000)
    {
        new millions = dollars / 1000000;
        new thousands = (dollars % 1000000) / 1000;
        new remainder = dollars % 1000;
        format(output, maxlength, "$%d,%03d,%03d.%02d", millions, thousands, remainder, cents);
    }
    else
    {
        new billions = dollars / 1000000000;
        new millions = (dollars % 1000000000) / 1000000;
        new thousands = (dollars % 1000000) / 1000;
        new remainder = dollars % 1000;
        format(output, maxlength, "$%d,%03d,%03d,%03d.%02d", billions, millions, thousands, remainder, cents);
    }
    return 1;
}

// Function to get required hours for level
stock GetRequiredHours(level)
{
    switch(level)
    {
        case 2: return 5;    // Level 2 needs 5 hours
        case 3: return 10;   // Level 3 needs 10 hours
        case 4: return 25;   // Level 4 needs 25 hours
        case 5: return 50;   // Level 5 needs 50 hours
    }
    return 0; // Level 1 or invalid
}

// Function to check and update player level based on playing time
stock CheckPlayerLevel(playerid)
{
    new hours = PlayerInfo[playerid][pPlayingTime] / 3600; // Convert seconds to hours
    new newLevel = 1;

    if(hours >= 50) newLevel = 5;
    else if(hours >= 25) newLevel = 4;
    else if(hours >= 10) newLevel = 3;
    else if(hours >= 5) newLevel = 2;
    else newLevel = 1;

    if(newLevel > PlayerInfo[playerid][pLevel])
    {
        PlayerInfo[playerid][pLevel] = newLevel;
        SetPlayerScore(playerid, PlayerInfo[playerid][pLevel]); // Score shows level

        new levelUpMsg[128];
        format(levelUpMsg, sizeof(levelUpMsg),
            "{00FF00}Selamat! Anda naik ke Level %d! (Jam main: %d jam)",
            newLevel, hours);
        SendClientMessage(playerid, 0x00FF00FF, levelUpMsg);
    }
    return 1;
}

// Function to format playing time
stock FormatPlayingTime(seconds, output[], maxlength)
{
    new hours = seconds / 3600;
    new minutes = (seconds % 3600) / 60;
    new secs = seconds % 60;

    format(output, maxlength, "%02d:%02d:%02d", hours, minutes, secs);
    return 1;
}

// Function to set login camera (random only, no cycling)
stock SetLoginCamera(playerid)
{
    // Enable spectating mode first for camera to work
    TogglePlayerSpectating(playerid, true);

    // Use timer to set camera after spectating is active (small delay needed)
    new funcname[] = "SetRandomCameraPosition";
    new formatstr[] = "i";
    SetTimerEx(funcname, 100, false, formatstr, playerid); // 100ms delay

    return 1;
}

// Function to set random camera position with smooth movement (called with delay)
forward SetRandomCameraPosition(playerid);
public SetRandomCameraPosition(playerid)
{
    if(!IsPlayerConnected(playerid) || PlayerInfo[playerid][pLogged])
        return 1;

    // Set random camera (0-2) with smooth interpolation movement
    new randomCamera = random(3);

    // Set camera based on random selection with smooth movement
    switch(randomCamera)
    {
        case 0: // Camera 1 (Laut) - Moving from sea to shore
        {
            InterpolateCameraPos(playerid, 725.454650, -1487.855102, 4.605409, 725.885131, -1936.155883, 6.852395, 120000);
            InterpolateCameraLookAt(playerid, 725.315429, -1492.847900, 4.375023, 725.912658, -1931.156250, 6.793802, 120000);
        }
        case 1: // Camera 2 (Darat) - Moving across the city
        {
            InterpolateCameraPos(playerid, 1338.505371, -1144.868041, 25.941522, 802.956665, -1146.203979, 26.249814, 120000);
            InterpolateCameraLookAt(playerid, 1333.505859, -1144.836181, 25.882928, 807.955749, -1146.113769, 26.218564, 120000);
        }
        case 2: // Camera 3 (Udara) - Flying through the air
        {
            InterpolateCameraPos(playerid, 1602.854858, -1744.372436, 70.099388, 1603.425903, -1418.095092, 62.731101, 120000);
            InterpolateCameraLookAt(playerid, 1603.038330, -1739.535156, 68.847549, 1603.119018, -1423.012573, 61.880119, 120000);
        }
    }

    return 1;
}

// ChangeLoginCamera function removed - no longer needed since camera is static

// Function to stop login camera (no timer to stop since camera is static)
stock StopLoginCamera(playerid)
{
    #pragma unused playerid
    // No timer to stop, just reset camera behind player
    return 1;
}

// Function to start AFK timer (3 minutes)
stock StartAFKTimer(playerid)
{
    new funcname[] = "AFKKickPlayer";
    new formatstr[] = "i";
    PlayerInfo[playerid][pAFKTimer] = SetTimerEx(funcname, 180000, false, formatstr, playerid); // 180 seconds (3 minutes)
    return 1;
}

// Function to stop AFK timer
stock StopAFKTimer(playerid)
{
    if(PlayerInfo[playerid][pAFKTimer] != 0)
    {
        KillTimer(PlayerInfo[playerid][pAFKTimer]);
        PlayerInfo[playerid][pAFKTimer] = 0;
    }
    return 1;
}

// Function to kick AFK player
forward AFKKickPlayer(playerid);
public AFKKickPlayer(playerid)
{
    if(!IsPlayerConnected(playerid) || PlayerInfo[playerid][pLogged])
    {
        // Player already logged in or disconnected
        return 1;
    }

    // Show AFK kick message
    Dialog_Show(playerid, AFKKick, DIALOG_STYLE_MSGBOX, "AFK Kick",
        "{FF0000}Anda telah di-kick dari server!\n\n{FFFFFF}Alasan: {FFFF00}AFK di login screen\n{FFFFFF}Waktu maksimal: {FFFF00}3 menit\n\n{FFFFFF}Silakan connect kembali dan login/register segera.",
        "OK", "");

    // Kick player after 3 seconds
    new funcname[] = "KickPlayerDelayed";
    new formatstr[] = "i";
    SetTimerEx(funcname, 3000, false, formatstr, playerid);
    return 1;
}

// Function to format relative time (last login)
stock FormatRelativeTime(timestamp, output[], maxlength)
{
    if(timestamp == 0)
    {
        format(output, maxlength, "Belum pernah login");
        return 1;
    }

    new currentTime = gettime();
    new diff = currentTime - timestamp;

    if(diff < 60) // Less than 1 minute
    {
        format(output, maxlength, "%d detik yang lalu", diff);
    }
    else if(diff < 3600) // Less than 1 hour
    {
        new minutes = diff / 60;
        format(output, maxlength, "%d menit yang lalu", minutes);
    }
    else if(diff < 86400) // Less than 1 day
    {
        new hours = diff / 3600;
        format(output, maxlength, "%d jam yang lalu", hours);
    }
    else // More than 1 day, show date
    {
        new year, month, day, hour, minute, second;
        TimestampToDate(timestamp, year, month, day, hour, minute, second);

        // Get day name using accurate calculation
        new dayName[20];
        GetAccurateDayName(day, month, year, dayName, sizeof(dayName));

        // Get month name
        new monthName[20];
        GetMonthName(month, monthName, sizeof(monthName));

        format(output, maxlength, "%s, %d %s %d", dayName, day, monthName, year);
    }
    return 1;
}

// Function to get day name from timestamp
stock GetDayName(timestamp, output[], maxlength)
{
    new dayNames[][] = {
        "Minggu", "Senin", "Selasa", "Rabu", "Kamis", "Jumat", "Sabtu"
    };

    // Calculate day of week (0 = Sunday)
    new days = timestamp / 86400;
    new dayOfWeek = (days + 4) % 7; // Adjust for epoch starting on Thursday

    format(output, maxlength, "%s", dayNames[dayOfWeek]);
    return 1;
}

// Function to get month name
stock GetMonthName(month, output[], maxlength)
{
    new monthNames[][] = {
        "", "Januari", "Februari", "Maret", "April", "Mei", "Juni",
        "Juli", "Agustus", "September", "Oktober", "November", "Desember"
    };

    if(month >= 1 && month <= 12)
    {
        format(output, maxlength, "%s", monthNames[month]);
    }
    else
    {
        format(output, maxlength, "Unknown");
    }
    return 1;
}

// Function to convert timestamp to date
stock TimestampToDate(timestamp, &year, &month, &day, &hour, &minute, &second)
{
    // Simple timestamp to date conversion
    new days = timestamp / 86400;
    new seconds_today = timestamp % 86400;

    hour = seconds_today / 3600;
    minute = (seconds_today % 3600) / 60;
    second = seconds_today % 60;

    // Approximate date calculation (starting from 1970)
    year = 1970;
    while(days >= 365)
    {
        if((year % 4 == 0 && year % 100 != 0) || (year % 400 == 0))
        {
            if(days >= 366)
            {
                days -= 366;
                year++;
            }
            else break;
        }
        else
        {
            days -= 365;
            year++;
        }
    }

    // Simple month calculation
    new daysInMonth[] = {31, 28, 31, 30, 31, 30, 31, 31, 30, 31, 30, 31};
    if((year % 4 == 0 && year % 100 != 0) || (year % 400 == 0))
        daysInMonth[1] = 29;

    month = 1;
    while(days >= daysInMonth[month-1] && month <= 12)
    {
        days -= daysInMonth[month-1];
        month++;
    }

    day = days + 1;
    return 1;
}

// ShowLastLoginInfo function removed - functionality moved to UnfreezePlayer

// Function to get current date and time in Indonesian format
stock GetCurrentDate(output[], maxlength)
{
    // Use SA-MP's getdate and gettime functions for accurate local time
    new year, month, day;
    new hour, minute, second;

    getdate(year, month, day);
    gettime(hour, minute, second);

    // Get day name using accurate calculation
    new dayName[20];
    GetAccurateDayName(day, month, year, dayName, sizeof(dayName));

    // Get month name
    new monthName[20];
    GetMonthName(month, monthName, sizeof(monthName));

    format(output, maxlength, "%s, %d %s %d - %02d:%02d:%02d", dayName, day, monthName, year, hour, minute, second);
    return 1;
}

// Function to unfreeze player after loading
forward UnfreezePlayer(playerid);
public UnfreezePlayer(playerid)
{
    if(!IsPlayerConnected(playerid) || !PlayerInfo[playerid][pLogged])
        return 1;

    // Unfreeze player
    TogglePlayerControllable(playerid, true);

    // Calculate load time in milliseconds
    new loadTime = GetTickCount() - PlayerInfo[playerid][pLoadStartTime];

    // Format last login time
    new lastLoginStr[64];
    FormatRelativeTime(PlayerInfo[playerid][pLastLogin], lastLoginStr, sizeof(lastLoginStr));

    // Get current date
    new currentDate[64];
    GetCurrentDate(currentDate, sizeof(currentDate));

    // Show different messages for new players vs returning players
    if(PlayerInfo[playerid][pIsNewPlayer])
    {
        // New player messages
        SendClientMessage(playerid, 0x00FF00FF, "Character creation complete! Welcome to the server!");

        new welcomeMsg[128];
        format(welcomeMsg, sizeof(welcomeMsg),
            "{00FF00}Selamat datang di server, {FFFFFF}%s{00FF00}! Selamat bermain!", PlayerInfo[playerid][pName]);
        SendClientMessage(playerid, 0x00FF00FF, welcomeMsg);

        SendClientMessage(playerid, 0xFFFF00FF, "TIP: Gunakan /help untuk melihat command yang tersedia!");
        SendClientMessage(playerid, 0xFFFF00FF, "TIP: Gunakan /stats untuk melihat informasi character Anda!");
    }
    else
    {
        // Returning player messages
        SendClientMessage(playerid, COLOR_KAKZAH, "SYSTEM: {FFFF00}Loading complete! Welcome back to the server!");

        new welcomeMsg[128];
        format(welcomeMsg, sizeof(welcomeMsg),
            "SERVER: {FFFFFF}Login berhasil! Selamat datang kembali, {9ACD32}%s", PlayerInfo[playerid][pName]);
        SendClientMessage(playerid, COLOR_KAKZAH, welcomeMsg);
    }

    // Show stats for both new and returning players
    new statsMsg[256];
    new moneyStr[32], timeStr[32];
    FormatMoney(PlayerInfo[playerid][pMoney], moneyStr, sizeof(moneyStr));

    // Calculate total playing time including current session
    new totalPlayingTime = PlayerInfo[playerid][pPlayingTime];
    if(PlayerInfo[playerid][pLoginTime] > 0)
    {
        new currentSession = gettime() - PlayerInfo[playerid][pLoginTime];
        totalPlayingTime += currentSession;
    }

    FormatPlayingTime(totalPlayingTime, timeStr, sizeof(timeStr));
    format(statsMsg, sizeof(statsMsg),
        "INFO: {FFFFFF}Level: {FFFF00}%d {FFFFFF}| Money: {00FF00}%s {FFFFFF}| Jam Main: {FFFF00}%s",
        PlayerInfo[playerid][pLevel], moneyStr, timeStr);
    SendClientMessage(playerid, COLOR_KAKZAH, statsMsg);

    // Show last login message only for returning players
    if(!PlayerInfo[playerid][pIsNewPlayer])
    {
        new lastLoginMsg[128];
        format(lastLoginMsg, sizeof(lastLoginMsg),
            "INFO: {FFFFFF}Anda Terakhir login: {FFFF00}%s", lastLoginStr);
        SendClientMessage(playerid, COLOR_KAKZAH, lastLoginMsg);
    }

    // Show load time message with different text
    new loadTimeMsg[128];
    if(PlayerInfo[playerid][pIsNewPlayer])
    {
        format(loadTimeMsg, sizeof(loadTimeMsg),
            "INFO: {FFFFFF}Character dibuat dalam: {00FFFF}%d ms", loadTime);
    }
    else
    {
        format(loadTimeMsg, sizeof(loadTimeMsg),
            "INFO: {FFFFFF}Data character dimuat dalam: {00FF00}%d ms", loadTime);
    }
    SendClientMessage(playerid, COLOR_KAKZAH, loadTimeMsg);

    // Show current date
    new dateMsg[128];
    format(dateMsg, sizeof(dateMsg),
        "INFO: {FFFFFF}Hari ini adalah {FFFF00}%s", currentDate);
    SendClientMessage(playerid, COLOR_KAKZAH, dateMsg);

    // Reset new player flag after showing messages
    PlayerInfo[playerid][pIsNewPlayer] = 0;

    // Check if player was dead when they disconnected - apply death mode AFTER loading complete
    if(PlayerInfo[playerid][pIsDead])
    {
        // Apply death effects now that character loading is complete
        new funcname[] = "ApplyDeathModeImmediate";
        new formatstr[] = "i";
        SetTimerEx(funcname, 1000, false, formatstr, playerid);

        SendClientMessage(playerid, 0xFF6347FF, "INJURED: Kamu masih dalam keadaan mati.");
    }

    return 1;
}

// Function to kick player with delay
forward KickPlayerDelayed(playerid);
public KickPlayerDelayed(playerid)
{
    Kick(playerid);
    return 1;
}

// Fungsi untuk hash password sederhana
stock HashPassword(const password[], hash[], maxlength)
{
    new hashvalue = 0;
    new len = strlen(password);

    // Security check - password should not be empty
    if(len == 0)
    {
        hash[0] = 0;
        return 0;
    }

    for(new i = 0; i < len; i++)
    {
        hashvalue = hashvalue * 31 + password[i];
    }

    format(hash, maxlength, "%08x%08x", hashvalue, hashvalue ^ 0xAAAAAAAA);
    return 1;
}

public OnGameModeInit()
{
    print("========================================");
    print("     MySQL Login/Register System");
    print("========================================");
    
    // Koneksi ke database MySQL
    g_SQL = mysql_connect(MYSQL_HOST, MYSQL_USER, MYSQL_PASS, MYSQL_DATABASE);
    
    if(mysql_errno(g_SQL) != 0)
    {
        print("ERROR: Koneksi ke database MySQL gagal!");
        print("Silakan periksa konfigurasi database Anda.");
        new exit_cmd[] = "exit";
        SendRconCommand(exit_cmd);
        return 1;
    }
    
    print("Database MySQL berhasil terhubung!");
    
    // Buat tabel jika belum ada
    new create_table_query[] = "\
        CREATE TABLE IF NOT EXISTS `players` (\
        `id` int(11) NOT NULL AUTO_INCREMENT,\
        `username` varchar(24) NOT NULL,\
        `password` varchar(129) NOT NULL,\
        `gender` int(1) DEFAULT 0,\
        `origin` varchar(32) DEFAULT 'Indonesia',\
        `birth_day` int(2) DEFAULT 1,\
        `birth_month` int(2) DEFAULT 1,\
        `birth_year` int(4) DEFAULT 2000,\
        `spawn_location` int(1) DEFAULT 0,\
        `level` int(11) DEFAULT 1,\
        `money` int(11) DEFAULT 5000,\
        `pos_x` float DEFAULT 1685.7581,\
        `pos_y` float DEFAULT -2334.8354,\
        `pos_z` float DEFAULT 13.5469,\
        `skin` int(11) DEFAULT 0,\
        `playing_time` int(11) DEFAULT 0,\
        `last_login` int(11) DEFAULT 0,\
        `admin_level` int(1) DEFAULT 0,\
        `admin_name` varchar(32) DEFAULT '',\
        `angle` float DEFAULT 0.0,\
        `total_duty_time` int(11) DEFAULT 0,\
        `health` float DEFAULT 100.0,\
        `armour` float DEFAULT 0.0,\
        `max_health` float DEFAULT 100.0,\
        `warn_count` int(2) DEFAULT 0,\
        `jailed` int(1) DEFAULT 0,\
        `jail_time` int(11) DEFAULT 0,\
        `banned` int(1) DEFAULT 0,\
        `ban_time` int(11) DEFAULT 0,\
        `ban_reason` varchar(128) DEFAULT '',\
        `hunger` int(3) DEFAULT 100,\
        `bladder` int(3) DEFAULT 100,\
        `energy` int(3) DEFAULT 100,\
        `hbe_mode` int(1) DEFAULT 0,\
        `registered` timestamp DEFAULT CURRENT_TIMESTAMP,\
        PRIMARY KEY (`id`),\
        UNIQUE KEY `username` (`username`)\
        ) ENGINE=MyISAM DEFAULT CHARSET=latin1;";
    
    mysql_tquery(g_SQL, create_table_query);

    // Create warns table
    new create_warns_table[] = "\
        CREATE TABLE IF NOT EXISTS `player_warns` (\
        `id` int(11) NOT NULL AUTO_INCREMENT,\
        `player_name` varchar(24) NOT NULL,\
        `warn_type` varchar(16) NOT NULL,\
        `admin_name` varchar(32) NOT NULL,\
        `warn_date` varchar(32) NOT NULL,\
        `reason` varchar(128) NOT NULL,\
        `timestamp` timestamp DEFAULT CURRENT_TIMESTAMP,\
        PRIMARY KEY (`id`),\
        KEY `player_name` (`player_name`)\
        ) ENGINE=MyISAM DEFAULT CHARSET=latin1;";

    mysql_tquery(g_SQL, create_warns_table);

    // Create player_weapon table
    new create_weapon_table[] = "\
        CREATE TABLE IF NOT EXISTS `player_weapon` (\
        `id` int(11) NOT NULL AUTO_INCREMENT,\
        `player_id` int(11) NOT NULL,\
        `weapon_slot` int(2) NOT NULL,\
        `weapon_id` int(3) NOT NULL,\
        `ammo` int(11) NOT NULL DEFAULT 0,\
        `magazine_size` int(11) NOT NULL DEFAULT 0,\
        `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,\
        `updated_at` timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,\
        PRIMARY KEY (`id`),\
        UNIQUE KEY `player_weapon_slot` (`player_id`, `weapon_slot`),\
        KEY `player_id` (`player_id`)\
        ) ENGINE=MyISAM DEFAULT CHARSET=latin1;";

    mysql_tquery(g_SQL, create_weapon_table);

    // Create player_inventory table
    new create_inventory_table[] = "\
        CREATE TABLE IF NOT EXISTS `player_inventory` (\
        `id` int(11) NOT NULL AUTO_INCREMENT,\
        `player_id` int(11) NOT NULL,\
        `item_type` varchar(32) NOT NULL,\
        `item_name` varchar(64) NOT NULL,\
        `quantity` int(11) NOT NULL DEFAULT 1,\
        `item_data` varchar(128) DEFAULT '',\
        `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,\
        `updated_at` timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,\
        PRIMARY KEY (`id`),\
        KEY `player_id` (`player_id`),\
        KEY `item_type` (`item_type`)\
        ) ENGINE=MyISAM DEFAULT CHARSET=latin1;";

    mysql_tquery(g_SQL, create_inventory_table);

    // Repair table if crashed and add missing columns
    new repair_query[] = "REPAIR TABLE `players`";
    mysql_tquery(g_SQL, repair_query);

    // Add missing columns if they don't exist (for existing databases)
    new alter_queries[][] = {
        "ALTER TABLE `players` ADD COLUMN `skin` int(11) DEFAULT 0",
        "ALTER TABLE `players` ADD COLUMN `playing_time` int(11) DEFAULT 0",
        "ALTER TABLE `players` ADD COLUMN `last_login` int(11) DEFAULT 0",
        "ALTER TABLE `players` ADD COLUMN `admin_level` int(1) DEFAULT 0",
        "ALTER TABLE `players` ADD COLUMN `admin_name` varchar(32) DEFAULT ''",
        "ALTER TABLE `players` ADD COLUMN `angle` float DEFAULT 0.0",
        "ALTER TABLE `players` ADD COLUMN `total_duty_time` int(11) DEFAULT 0",
        "ALTER TABLE `players` ADD COLUMN `health` float DEFAULT 100.0",
        "ALTER TABLE `players` ADD COLUMN `armour` float DEFAULT 0.0",
        "ALTER TABLE `players` ADD COLUMN `max_health` float DEFAULT 100.0",
        "ALTER TABLE `players` ADD COLUMN `warn_count` int(2) DEFAULT 0",
        "ALTER TABLE `players` ADD COLUMN `jailed` int(1) DEFAULT 0",
        "ALTER TABLE `players` ADD COLUMN `jail_time` int(11) DEFAULT 0",
        "ALTER TABLE `players` ADD COLUMN `banned` int(1) DEFAULT 0",
        "ALTER TABLE `players` ADD COLUMN `ban_time` int(11) DEFAULT 0",
        "ALTER TABLE `players` ADD COLUMN `ban_reason` varchar(128) DEFAULT ''",
        "ALTER TABLE `players` ADD COLUMN `hunger` int(3) DEFAULT 100",
        "ALTER TABLE `players` ADD COLUMN `bladder` int(3) DEFAULT 100",
        "ALTER TABLE `players` ADD COLUMN `energy` int(3) DEFAULT 100",
        "ALTER TABLE `players` ADD COLUMN `hbe_mode` int(1) DEFAULT 0",
        "ALTER TABLE `players` ADD COLUMN `body_part_conditions` varchar(32) DEFAULT '0,0,0,0,0,0,0'"
    };

    for(new i = 0; i < sizeof(alter_queries); i++)
    {
        mysql_tquery(g_SQL, alter_queries[i]);
    }

    // Start auto-save timer (every 5 minutes = 300000 ms)
    new funcname[] = "AutoSaveAllPlayers";
    SetTimer(funcname, 300000, true);
    print("Auto-save timer started (every 5 minutes)");

    // Create red screen textdraw for death system
    RedScreen = TextDrawCreate(317.000000, -400.000000, "Tabrakan");
    TextDrawFont(RedScreen, 1);
    TextDrawLetterSize(RedScreen, 200.258332, 100.750000);
    TextDrawTextSize(RedScreen, 1000.500000, 1000.500000);
    TextDrawSetOutline(RedScreen, 1);
    TextDrawSetShadow(RedScreen, 0);
    TextDrawAlignment(RedScreen, 2);
    TextDrawColor(RedScreen, 0xFF000011);
    TextDrawBackgroundColor(RedScreen, 255);
    TextDrawBoxColor(RedScreen, 0xFF000011);
    TextDrawUseBox(RedScreen, 1);

    // Initialize time and weather system
    new funcname1[] = "UpdateTimeForAllPlayers";
    SetTimer(funcname1, 1000, true); // Update time every second
    print("Time system started (updates every second)");

    new funcname2[] = "UpdateWeatherSystem";
    SetTimer(funcname2, 60000, true); // Check weather every minute
    print("Weather system started (checks every minute)");

    // Set random initial weather
    new randomIndex = random(sizeof(gWeatherList));
    gCurrentWeather = gWeatherList[randomIndex];
    SetWeather(gCurrentWeather);
    printf("Random initial weather set to ID %d", gCurrentWeather);

    // Set initial world time to match real time
    new hour, minute, second;
    gettime(hour, minute, second);
    SetWorldTime(hour);
    printf("World time synchronized to %02d:00", hour);

    return 1;
}

// Auto-save function called every 5 minutes
forward AutoSaveAllPlayers();
public AutoSaveAllPlayers()
{
    new savedCount = 0;
    for(new i = 0; i < MAX_PLAYERS; i++)
    {
        if(IsPlayerConnected(i) && PlayerInfo[i][pLogged])
        {
            SavePlayerData(i);
            savedCount++;
        }
    }

    if(savedCount > 0)
    {
        printf("Auto-save: %d players data saved", savedCount);
    }

    return 1;
}

public OnGameModeExit()
{
    mysql_close(g_SQL);
    return 1;
}

public OnPlayerConnect(playerid)
{
    // Get player name (validation will be done after camera move)
    GetPlayerName(playerid, PlayerInfo[playerid][pName], MAX_PLAYER_NAME);

    // Reset player data
    PlayerInfo[playerid][pID] = 0;
    PlayerInfo[playerid][pPassword][0] = 0;
    PlayerInfo[playerid][pLevel] = 1;
    PlayerInfo[playerid][pMoney] = 5000;
    PlayerInfo[playerid][pPosX] = 0.0;
    PlayerInfo[playerid][pPosY] = 0.0;
    PlayerInfo[playerid][pPosZ] = 0.0;
    PlayerInfo[playerid][pLogged] = false;
    PlayerInfo[playerid][pSkin] = 0;
    PlayerInfo[playerid][pSpawnLocation] = 0;
    PlayerInfo[playerid][pPlayingTime] = 0;
    PlayerInfo[playerid][pLoginTime] = 0;
    PlayerInfo[playerid][pAFKTimer] = 0;
    PlayerInfo[playerid][pLoginAttempts] = 0;
    PlayerInfo[playerid][pLastLogin] = 0;
    PlayerInfo[playerid][pLoadStartTime] = GetTickCount(); // Start load time tracking
    PlayerInfo[playerid][pAdoText] = Text3D:INVALID_3DTEXT_ID;
    PlayerInfo[playerid][pAdminLevel] = 0;
    PlayerInfo[playerid][pAdminDuty] = 0;
    PlayerInfo[playerid][pAdminName][0] = 0;
    PlayerInfo[playerid][pAngle] = 0.0;
    PlayerInfo[playerid][pDutyStartTime] = 0;
    PlayerInfo[playerid][pTotalDutyTime] = 0;
    PlayerInfo[playerid][pOriginPage] = 0;
    PlayerInfo[playerid][pIsNewPlayer] = 0;
    PlayerInfo[playerid][pAdminListPage] = 0;
    PlayerInfo[playerid][pSelectedAdminID] = 0;
    PlayerInfo[playerid][pHealth] = 100.0;
    PlayerInfo[playerid][pArmour] = 0.0;
    PlayerInfo[playerid][pMaxHealth] = 100.0;
    PlayerInfo[playerid][pWarnCount] = 0;
    PlayerInfo[playerid][pInterior] = 0;
    PlayerInfo[playerid][pVirtualWorld] = 0;

    // Initialize death system variables
    PlayerInfo[playerid][pIsDead] = false;
    PlayerInfo[playerid][pDeathTimer] = 0;
    PlayerInfo[playerid][pShakeTimer] = 0;
    PlayerInfo[playerid][pAnimTimer] = 0;
    PlayerInfo[playerid][pDeathX] = 0.0;
    PlayerInfo[playerid][pDeathY] = 0.0;
    PlayerInfo[playerid][pDeathZ] = 0.0;
    PlayerInfo[playerid][pDeathAngle] = 0.0;

    // Initialize jail system variables
    PlayerInfo[playerid][pJailed] = false;
    PlayerInfo[playerid][pJailTime] = 0;
    PlayerInfo[playerid][pJailTimer] = 0;
    for(new i = 0; i < 4; i++)
    {
        PlayerInfo[playerid][pJailTextDraw][i] = PlayerText:INVALID_TEXT_DRAW;
    }

    // Initialize time system variables
    for(new i = 0; i < 2; i++)
    {
        PlayerInfo[playerid][pTimeTextDraw][i] = PlayerText:INVALID_TEXT_DRAW;
    }

    // Initialize ban system variables
    PlayerInfo[playerid][pBanned] = false;
    PlayerInfo[playerid][pBanTime] = 0;
    PlayerInfo[playerid][pBanReason][0] = 0;

    // Initialize HBE system variables
    PlayerInfo[playerid][pHunger] = 100;
    PlayerInfo[playerid][pBladder] = 100;
    PlayerInfo[playerid][pEnergy] = 100;
    PlayerInfo[playerid][pHBETimer] = 0;
    PlayerInfo[playerid][pIsPissing] = false;
    PlayerInfo[playerid][pPissTimer] = 0;
    PlayerInfo[playerid][pHBEMode] = 0; // Default to Classic mode

    // Initialize Body Part Status System
    for(new i = 0; i < 7; i++)
    {
        PlayerInfo[playerid][pBodyPartCondition][i] = CONDITION_NORMAL;
    }
    PlayerInfo[playerid][pBodyPartTimer] = 0;

    // Initialize Custom Weapon System
    for(new i = 0; i < 13; i++)
    {
        PlayerInfo[playerid][pWeaponAmmo][i] = 0;
        PlayerInfo[playerid][pWeaponMagazine][i] = 0;
        PlayerInfo[playerid][pInventoryWeapons][i] = 0;
        PlayerInfo[playerid][pInventoryAmmoUnits][i] = 0;
    }
    PlayerInfo[playerid][pCurrentWeapon] = 0;
    PlayerInfo[playerid][pAmmoTDShown] = false;
    PlayerInfo[playerid][pInventoryMoney] = 0;
    PlayerInfo[playerid][pSelectedWeaponSlot] = -1;

    // Create ammo textdraw for player
    PlayerInfo[playerid][pAmmoTD] = TextDrawCreate(521.000000, 64.000000, "100");
    TextDrawFont(PlayerInfo[playerid][pAmmoTD], 1);
    TextDrawLetterSize(PlayerInfo[playerid][pAmmoTD], 0.337500, 1.649999);
    TextDrawTextSize(PlayerInfo[playerid][pAmmoTD], 400.000000, 17.000000);
    TextDrawSetOutline(PlayerInfo[playerid][pAmmoTD], 1);
    TextDrawSetShadow(PlayerInfo[playerid][pAmmoTD], 0);
    TextDrawAlignment(PlayerInfo[playerid][pAmmoTD], 2);
    TextDrawColor(PlayerInfo[playerid][pAmmoTD], -16776961);
    TextDrawBackgroundColor(PlayerInfo[playerid][pAmmoTD], 255);
    TextDrawBoxColor(PlayerInfo[playerid][pAmmoTD], 50);
    TextDrawUseBox(PlayerInfo[playerid][pAmmoTD], 0);
    TextDrawSetProportional(PlayerInfo[playerid][pAmmoTD], 1);
    TextDrawSetSelectable(PlayerInfo[playerid][pAmmoTD], 0);



    // Initialize HBE textdraws and progress bars
    for(new i = 0; i < 10; i++)
    {
        PlayerTD[playerid][i] = PlayerText:INVALID_TEXT_DRAW;
    }
    for(new i = 0; i < 3; i++)
    {
        PlayerProgressBar[playerid][i] = PlayerBar:INVALID_PLAYER_BAR_ID;
    }

    // Initialize pagination variables
    PlayerWarnsPage[playerid] = 1;
    PlayerWarnsTarget[playerid][0] = 0;
    PlayerAHelpPage[playerid] = 1;

    // Set login camera (spectating enabled inside function)
    SetLoginCamera(playerid);

    // Start AFK timer (3 minutes)
    StartAFKTimer(playerid);

    // Cek apakah player sudah terdaftar
    new query[256];
    mysql_format(g_SQL, query, sizeof(query), "SELECT * FROM `players` WHERE `username` = '%e'", PlayerInfo[playerid][pName]);
    mysql_tquery(g_SQL, query, "OnPlayerCheckAccount", "i", playerid);
    
    return 1;
}

public OnPlayerDisconnect(playerid, reason)
{
    // Stop login camera timer if still running
    StopLoginCamera(playerid);

    // Stop AFK timer if still running
    StopAFKTimer(playerid);

    // Stop death timer if running
    if(PlayerInfo[playerid][pDeathTimer] != 0)
    {
        KillTimer(PlayerInfo[playerid][pDeathTimer]);
        PlayerInfo[playerid][pDeathTimer] = 0;
    }

    // Stop shake timer if running
    if(PlayerInfo[playerid][pShakeTimer] != 0)
    {
        KillTimer(PlayerInfo[playerid][pShakeTimer]);
        PlayerInfo[playerid][pShakeTimer] = 0;
    }

    // Stop animation timer if running
    if(PlayerInfo[playerid][pAnimTimer] != 0)
    {
        KillTimer(PlayerInfo[playerid][pAnimTimer]);
        PlayerInfo[playerid][pAnimTimer] = 0;
    }

    // Stop jail timer if running
    if(PlayerInfo[playerid][pJailTimer] != 0)
    {
        KillTimer(PlayerInfo[playerid][pJailTimer]);
        PlayerInfo[playerid][pJailTimer] = 0;
    }

    // Stop HBE timer if running
    if(PlayerInfo[playerid][pHBETimer] != 0)
    {
        KillTimer(PlayerInfo[playerid][pHBETimer]);
        PlayerInfo[playerid][pHBETimer] = 0;
    }

    // Stop piss timer if running
    if(PlayerInfo[playerid][pPissTimer] != 0)
    {
        KillTimer(PlayerInfo[playerid][pPissTimer]);
        PlayerInfo[playerid][pPissTimer] = 0;
    }

    // Cleanup Custom Weapon System
    if(PlayerInfo[playerid][pAmmoTDShown])
    {
        TextDrawHideForPlayer(playerid, PlayerInfo[playerid][pAmmoTD]);
        PlayerInfo[playerid][pAmmoTDShown] = false;
    }
    TextDrawDestroy(PlayerInfo[playerid][pAmmoTD]);



    // Destroy HBE textdraws and progress bars
    DestroyHBETextDraw(playerid);

    // Destroy jail textdraw if exists
    DestroyJailTextDraw(playerid);

    // Destroy time textdraw if exists
    DestroyTimeTextDraw(playerid);

    // Delete 3D text if exists
    if(PlayerInfo[playerid][pAdoText] != Text3D:INVALID_3DTEXT_ID)
    {
        Delete3DTextLabel(PlayerInfo[playerid][pAdoText]);
        PlayerInfo[playerid][pAdoText] = Text3D:INVALID_3DTEXT_ID;
    }

    // Admin label removed - using display name system instead

    if(PlayerInfo[playerid][pLogged])
    {
        // If admin is on duty when disconnecting, save the duty time
        if(PlayerInfo[playerid][pAdminDuty] && PlayerInfo[playerid][pAdminLevel] > 0 && PlayerInfo[playerid][pDutyStartTime] > 0)
        {
            new dutyTime = gettime() - PlayerInfo[playerid][pDutyStartTime];
            PlayerInfo[playerid][pTotalDutyTime] += dutyTime;
            PlayerInfo[playerid][pAdminDuty] = 0; // Set duty off for next login
            PlayerInfo[playerid][pDutyStartTime] = 0;
        }

        // Save current position, virtual world and interior before disconnect
        GetPlayerPos(playerid, PlayerInfo[playerid][pPosX], PlayerInfo[playerid][pPosY], PlayerInfo[playerid][pPosZ]);
        GetPlayerFacingAngle(playerid, PlayerInfo[playerid][pAngle]);
        PlayerInfo[playerid][pVirtualWorld] = GetPlayerVirtualWorld(playerid);
        PlayerInfo[playerid][pInterior] = GetPlayerInterior(playerid);

        SavePlayerData(playerid);
    }
    return 1;
}

forward OnPlayerCheckAccount(playerid);
public OnPlayerCheckAccount(playerid)
{
    // First, validate Roleplay Name format (after camera move)
    if(!IsValidRoleplayName(PlayerInfo[playerid][pName]))
    {
        Dialog_Show(playerid, RPNameError, DIALOG_STYLE_MSGBOX, "Nama Tidak Valid!",
            "{FF0000}Nama Anda tidak menggunakan format Roleplay!\n\n{FFFFFF}Format yang benar:\n{FFFF00}Firstname_Lastname\n\n{FFFFFF}Contoh: {00FF00}Jack_Heversont\n\n{FF0000}Anda akan di-kick dari server dalam 5 detik!",
            "OK", "");

        // Kick player after 5 seconds
        new funcname[] = "KickPlayerDelayed";
        new formatstr[] = "i";
        SetTimerEx(funcname, 5000, false, formatstr, playerid);
        return 1;
    }

    new rows = cache_num_rows();

    if(rows)
    {
        // Load password from database for validation
        cache_get_value(0, "password", PlayerInfo[playerid][pPassword], 129);

        // Security check - make sure password is not empty
        if(strlen(PlayerInfo[playerid][pPassword]) == 0)
        {
            SendClientMessage(playerid, 0xFF0000FF, "ERROR: Database error - password kosong!");
            Kick(playerid);
            return 1;
        }

        // Load last login timestamp (for later use when spawning)
        cache_get_value_int(0, "last_login", PlayerInfo[playerid][pLastLogin]);

        // Create login dialog with welcome message and login attempts (no last login info)
        new loginMsg[256];
        format(loginMsg, sizeof(loginMsg),
            "{FFFFFF}Selamat datang {00FF00}%s{FFFFFF}!\n\n\
            {FFFFFF}Kesempatan login: {FFFF00}(%d/5)\n\n\
            {FFFFFF}Masukkan password Anda untuk login:",
            PlayerInfo[playerid][pName], PlayerInfo[playerid][pLoginAttempts]);

        Dialog_Show(playerid, Login, DIALOG_STYLE_PASSWORD, "Login", loginMsg, "Login", "Keluar");
    }
    else
    {
        // Player belum terdaftar, tampilkan dialog register
        Dialog_Show(playerid, Register, DIALOG_STYLE_PASSWORD, "Registrasi",
            "{FFFFFF}Selamat datang!\n\n{FFFF00}Akun Anda belum terdaftar.\nMasukkan password yang ingin Anda gunakan:",
            "Daftar", "Keluar");
    }
    return 1;
}

// OnDialogResponse handled by easyDialog include

forward OnPlayerRegister(playerid);
public OnPlayerRegister(playerid)
{
    if(mysql_errno(g_SQL) != 0)
    {
        new error_msg[] = "ERROR: You don't have privilege to use this command";
        SendClientMessage(playerid, 0x999999FF, error_msg);
        Kick(playerid);
        return 1;
    }
    
    PlayerInfo[playerid][pID] = cache_insert_id();
    PlayerInfo[playerid][pLevel] = 1;
    PlayerInfo[playerid][pMoney] = 5000;
    PlayerInfo[playerid][pPlayingTime] = 0; // Start with 0 playing time
    PlayerInfo[playerid][pLoginTime] = gettime(); // Set login timestamp
    PlayerInfo[playerid][pLogged] = true;

    // Initialize admin duty status (new players are not admin and not on duty)
    PlayerInfo[playerid][pAdminDuty] = 0;
    PlayerInfo[playerid][pDutyStartTime] = 0;

    // Mark as new player for different welcome messages
    PlayerInfo[playerid][pIsNewPlayer] = 1;

    // Stop AFK timer since player registered
    StopAFKTimer(playerid);
    
    // Show registration success with formatted money
    new moneyStr[32];
    FormatMoney(PlayerInfo[playerid][pMoney], moneyStr, sizeof(moneyStr));

    new regMsg[256];
    format(regMsg, sizeof(regMsg),
        "{00FF00}Selamat! Akun Anda berhasil didaftarkan!\n{FFFFFF}Level: {FFFF00}%d {FFFFFF}| Starting Money: {00FF00}%s",
        PlayerInfo[playerid][pLevel], moneyStr);
    SendClientMessage(playerid, 0x00FF00FF, regMsg);
    
    ResetPlayerMoney(playerid);
    GivePlayerMoney(playerid, PlayerInfo[playerid][pMoney]);
    SetPlayerScore(playerid, PlayerInfo[playerid][pLevel]); // Score shows level

    // Set initial position for new player based on spawn location
    new spawnIndex = PlayerInfo[playerid][pSpawnLocation];
    PlayerInfo[playerid][pPosX] = gSpawnLocations[spawnIndex][0];
    PlayerInfo[playerid][pPosY] = gSpawnLocations[spawnIndex][1];
    PlayerInfo[playerid][pPosZ] = gSpawnLocations[spawnIndex][2];
    PlayerInfo[playerid][pAngle] = gSpawnLocations[spawnIndex][3]; // Use spawn location angle

    SetSpawnInfo(playerid, 0, PlayerInfo[playerid][pSkin],
        PlayerInfo[playerid][pPosX],
        PlayerInfo[playerid][pPosY],
        PlayerInfo[playerid][pPosZ],
        PlayerInfo[playerid][pAngle], // Use saved angle
        0, 0, 0, 0, 0, 0);
    SpawnPlayer(playerid);
    
    // Stop login camera and activate normal camera
    StopLoginCamera(playerid);
    TogglePlayerSpectating(playerid, false);
    SpawnPlayer(playerid);

    // Set player color to fully transparent (no map icon)
    SetPlayerColor(playerid, 0xFFFFFF00);

    // Freeze player during loading
    TogglePlayerControllable(playerid, false);

    // Show registration welcome text
    GameTextForPlayer(playerid, "~g~Welcome to Server!~n~~w~Creating Character..", 3000, 4);

    // Unfreeze player after loading complete (3 seconds)
    new funcname[] = "UnfreezePlayer";
    new formatstr[] = "i";
    SetTimerEx(funcname, 3000, false, formatstr, playerid);

    // Reset camera behind player after spawn
    SetCameraBehindPlayer(playerid);
    
    return 1;
}

forward OnPlayerLogin(playerid);
public OnPlayerLogin(playerid)
{
    new rows = cache_num_rows();

    // Security check - this function should only be called after password validation
    if(rows == 0)
    {
        SendClientMessage(playerid, 0xFF0000FF, "ERROR: Database error atau akun tidak ditemukan!");
        Kick(playerid);
        return 1;
    }

    // Additional security check - make sure player is not already logged in
    if(PlayerInfo[playerid][pLogged])
    {
        return 1;
    }

    // Login berhasil - load player data
    cache_get_value_int(0, "id", PlayerInfo[playerid][pID]);
    cache_get_value_int(0, "level", PlayerInfo[playerid][pLevel]);
    cache_get_value_int(0, "money", PlayerInfo[playerid][pMoney]);
    cache_get_value_float(0, "pos_x", PlayerInfo[playerid][pPosX]);
    cache_get_value_float(0, "pos_y", PlayerInfo[playerid][pPosY]);
    cache_get_value_float(0, "pos_z", PlayerInfo[playerid][pPosZ]);
    cache_get_value_int(0, "skin", PlayerInfo[playerid][pSkin]);
    cache_get_value_int(0, "spawn_location", PlayerInfo[playerid][pSpawnLocation]);
    cache_get_value_int(0, "playing_time", PlayerInfo[playerid][pPlayingTime]);
    cache_get_value_int(0, "admin_level", PlayerInfo[playerid][pAdminLevel]);
    cache_get_value(0, "admin_name", PlayerInfo[playerid][pAdminName], 32);
    cache_get_value_float(0, "angle", PlayerInfo[playerid][pAngle]);
    cache_get_value_int(0, "total_duty_time", PlayerInfo[playerid][pTotalDutyTime]);
    cache_get_value_float(0, "health", PlayerInfo[playerid][pHealth]);
    cache_get_value_float(0, "armour", PlayerInfo[playerid][pArmour]);
    cache_get_value_float(0, "max_health", PlayerInfo[playerid][pMaxHealth]);
    cache_get_value_int(0, "warn_count", PlayerInfo[playerid][pWarnCount]);
    cache_get_value_int(0, "gender", PlayerInfo[playerid][pGender]);
    cache_get_value(0, "origin", PlayerInfo[playerid][pOrigin], 32);
    cache_get_value_int(0, "interior", PlayerInfo[playerid][pInterior]);
    cache_get_value_int(0, "virtual_world", PlayerInfo[playerid][pVirtualWorld]);

    // Load jail data
    new jailed, jailTime;
    cache_get_value_int(0, "jailed", jailed);
    cache_get_value_int(0, "jail_time", jailTime);
    PlayerInfo[playerid][pJailed] = jailed ? true : false;
    PlayerInfo[playerid][pJailTime] = jailTime;
    PlayerInfo[playerid][pJailTimer] = 0;
    for(new i = 0; i < 4; i++)
    {
        PlayerInfo[playerid][pJailTextDraw][i] = PlayerText:INVALID_TEXT_DRAW;
    }

    // Initialize time system variables
    for(new i = 0; i < 2; i++)
    {
        PlayerInfo[playerid][pTimeTextDraw][i] = PlayerText:INVALID_TEXT_DRAW;
    }

    // Load ban data
    new banned, banTime;
    cache_get_value_int(0, "banned", banned);
    cache_get_value_int(0, "ban_time", banTime);
    cache_get_value(0, "ban_reason", PlayerInfo[playerid][pBanReason], 128);
    PlayerInfo[playerid][pBanned] = banned ? true : false;
    PlayerInfo[playerid][pBanTime] = banTime;

    // Load HBE data
    cache_get_value_int(0, "hunger", PlayerInfo[playerid][pHunger]);
    cache_get_value_int(0, "bladder", PlayerInfo[playerid][pBladder]);
    cache_get_value_int(0, "energy", PlayerInfo[playerid][pEnergy]);

    // Load HBE mode with fallback to Classic (0) if column doesn't exist
    new hbeMode;
    if(cache_get_value_int(0, "hbe_mode", hbeMode))
    {
        PlayerInfo[playerid][pHBEMode] = hbeMode;
    }
    else
    {
        PlayerInfo[playerid][pHBEMode] = 0; // Default to Classic mode
    }

    // Initialize HBE system variables
    PlayerInfo[playerid][pHBETimer] = 0;
    PlayerInfo[playerid][pIsPissing] = false;
    PlayerInfo[playerid][pPissTimer] = 0;
    // pHBEMode sudah di-load dari database, jangan di-reset

    // Load Body Part Status data
    new bodyPartConditionsStr[32];
    if(cache_get_value(0, "body_part_conditions", bodyPartConditionsStr, sizeof(bodyPartConditionsStr)))
    {
        StringToBodyPartConditions(playerid, bodyPartConditionsStr);
    }
    else
    {
        // Default values if column doesn't exist
        for(new i = 0; i < 7; i++)
        {
            PlayerInfo[playerid][pBodyPartCondition][i] = CONDITION_NORMAL;
        }
    }

    // Initialize body part timer
    PlayerInfo[playerid][pBodyPartTimer] = 0;

    // Initialize HBE textdraws and progress bars
    for(new i = 0; i < 10; i++)
    {
        PlayerTD[playerid][i] = PlayerText:INVALID_TEXT_DRAW;
    }
    for(new i = 0; i < 3; i++)
    {
        PlayerProgressBar[playerid][i] = PlayerBar:INVALID_PLAYER_BAR_ID;
    }

    // Check if player is banned
    if(PlayerInfo[playerid][pBanned])
    {
        new currentTime = gettime();

        // Check if ban is permanent (ban_time = 0) or still active
        if(PlayerInfo[playerid][pBanTime] == 0 || currentTime < PlayerInfo[playerid][pBanTime])
        {
            new banMessage[256];
            if(PlayerInfo[playerid][pBanTime] == 0)
            {
                format(banMessage, sizeof(banMessage),
                    "{FF0000}Anda telah di-ban secara permanent dari server ini!\n\n{FFFFFF}Alasan: {FFFF00}%s\n\n{FFFFFF}Untuk appeal ban, hubungi admin di forum/discord.",
                    PlayerInfo[playerid][pBanReason]);
            }
            else
            {
                new remainingTime = PlayerInfo[playerid][pBanTime] - currentTime;
                new days = remainingTime / 86400;
                new hours = (remainingTime % 86400) / 3600;
                new minutes = (remainingTime % 3600) / 60;

                format(banMessage, sizeof(banMessage),
                    "{FF0000}Anda telah di-ban dari server ini!\n\n{FFFFFF}Alasan: {FFFF00}%s\n\n{FFFFFF}Sisa waktu ban: {FFFF00}%d hari, %d jam, %d menit\n\n{FFFFFF}Untuk appeal ban, hubungi admin di forum/discord.",
                    PlayerInfo[playerid][pBanReason], days, hours, minutes);
            }

            Dialog_Show(playerid, BanMessage, DIALOG_STYLE_MSGBOX, "BANNED FROM SERVER", banMessage, "OK", "");

            // Kick player after 5 seconds
            new funcname[] = "KickPlayerDelayed";
            new formatstr[] = "i";
            SetTimerEx(funcname, 5000, false, formatstr, playerid);
            return 1;
        }
        else
        {
            // Ban expired, unban player
            PlayerInfo[playerid][pBanned] = false;
            PlayerInfo[playerid][pBanTime] = 0;
            PlayerInfo[playerid][pBanReason][0] = 0;

            SendClientMessage(playerid, 0x00FF00FF, "INFO: Ban Anda telah berakhir. Selamat datang kembali!");
        }
    }

    PlayerInfo[playerid][pLogged] = true;
    PlayerInfo[playerid][pLoginTime] = gettime(); // Set login timestamp

    // Reset admin duty status on login (admin must manually go on duty)
    PlayerInfo[playerid][pAdminDuty] = 0;
    PlayerInfo[playerid][pDutyStartTime] = 0;

    // Stop AFK timer since player logged in
    StopAFKTimer(playerid);

    // Check and update level based on playing time
    CheckPlayerLevel(playerid);

    // Load death status from database (check if player was dead when they disconnected)
    LoadPlayerDeathStatus(playerid);

    // Load player weapons from database
    LoadPlayerWeapons(playerid);

    // Welcome messages will be shown after loading in UnfreezePlayer function

    ResetPlayerMoney(playerid);
    GivePlayerMoney(playerid, PlayerInfo[playerid][pMoney]);
    SetPlayerScore(playerid, PlayerInfo[playerid][pLevel]); // Score shows level

    // Use saved position for spawn (jail will be handled after loading)
    SetSpawnInfo(playerid, 0, PlayerInfo[playerid][pSkin],
        PlayerInfo[playerid][pPosX],
        PlayerInfo[playerid][pPosY],
        PlayerInfo[playerid][pPosZ],
        PlayerInfo[playerid][pAngle], // Use saved angle
        0, 0, 0, 0, 0, 0);
    SpawnPlayer(playerid);
        
    // Stop login camera and activate normal camera
    StopLoginCamera(playerid);
    TogglePlayerSpectating(playerid, false);
    SpawnPlayer(playerid);

    // Set player color to fully transparent (no map icon)
    SetPlayerColor(playerid, 0xFFFFFF00);

    // Set player health and armour from saved data
    SetPlayerHealth(playerid, PlayerInfo[playerid][pHealth]);
    SetPlayerArmour(playerid, PlayerInfo[playerid][pArmour]);

    // Check if player needs to be put back in jail (will be handled after loading)
    if(PlayerInfo[playerid][pJailed] && PlayerInfo[playerid][pJailTime] > 0)
    {
        // Set timer to put player back in jail after spawn loading (3 seconds)
        new funcname[] = "PutPlayerBackInJail";
        new formatstr[] = "i";
        SetTimerEx(funcname, 3000, false, formatstr, playerid);
    }
    else
    {
        // Set virtual world and interior from saved data (only for non-jailed players)
        SetPlayerVirtualWorld(playerid, PlayerInfo[playerid][pVirtualWorld]);
        SetPlayerInterior(playerid, PlayerInfo[playerid][pInterior]);
    }

    // Freeze player during loading
    TogglePlayerControllable(playerid, false);

    // Show login welcome back text
    GameTextForPlayer(playerid, "~b~Welcome Back!~n~~w~Loading Character..", 3000, 4);

    // Unfreeze player after loading complete (3 seconds)
    new funcname[] = "UnfreezePlayer";
    new formatstr[] = "i";
    SetTimerEx(funcname, 3000, false, formatstr, playerid);

    // Reset camera behind player after spawn
    SetCameraBehindPlayer(playerid);
    return 1;
}

public OnPlayerDeath(playerid, killerid, reason)
{
    if(!PlayerInfo[playerid][pLogged]) return 1;

    // Don't apply animation here - will be applied after spawn in OnPlayerSpawn

    // Save death position and angle
    GetPlayerPos(playerid, PlayerInfo[playerid][pDeathX], PlayerInfo[playerid][pDeathY], PlayerInfo[playerid][pDeathZ]);
    GetPlayerFacingAngle(playerid, PlayerInfo[playerid][pDeathAngle]);

    // Set player as waiting for accept death
    PlayerInfo[playerid][pIsDead] = true;

    // Start death timer (3 minutes = 180000 ms) - player must wait before using /acceptdeath
    new funcname[] = "OnDeathTimerEnd";
    new formatstr[] = "i";
    PlayerInfo[playerid][pDeathTimer] = SetTimerEx(funcname, 180000, false, formatstr, playerid);

    // Set health and effects
    SetPlayerHealth(playerid, 100.0);
    SetPlayerDrunkLevel(playerid, 999999);

    // Don't freeze player with system, let animation handle movement restriction
    TogglePlayerControllable(playerid, true);

    // Save death status to database immediately
    SavePlayerDeathStatus(playerid);

    // Auto spawn to apply death state with short delay (500ms to prevent skin selection)
    new funcname2[] = "ApplyDeathState";
    new formatstr2[] = "i";
    SetTimerEx(funcname2, 500, false, formatstr2, playerid);

    return 0; // Prevent default spawn
}

public OnPlayerText(playerid, text[])
{
    if(!PlayerInfo[playerid][pLogged]) return 0;

    new string[256], name[MAX_PLAYER_NAME];
    GetPlayerDisplayName(playerid, name, sizeof(name));

    // If player is jailed, convert normal chat to OOC format with cyan name
    if(PlayerInfo[playerid][pJailed])
    {
        format(string, sizeof(string), "(( {00FFFF}%s{E6E6FA}: %s ))", name, text);
    }
    // If admin is on duty, convert normal chat to OOC format with red name
    else if(PlayerInfo[playerid][pAdminDuty] && PlayerInfo[playerid][pAdminLevel] > 0)
    {
        format(string, sizeof(string), "(( {FF0000}%s{E6E6FA}: %s ))", name, text);
    }
    else
    {
        // Normal IC chat
        format(string, sizeof(string), "%s says: %s", name, text);
    }

    // Send to nearby players (10 meter radius - realistic talking distance)
    new Float:x, Float:y, Float:z;
    GetPlayerPos(playerid, x, y, z);

    for(new i = 0; i < MAX_PLAYERS; i++)
    {
        if(IsPlayerConnected(i) && PlayerInfo[i][pLogged])
        {
            if(GetPlayerVirtualWorld(i) == GetPlayerVirtualWorld(playerid) && GetPlayerInterior(i) == GetPlayerInterior(playerid))
            {
                new Float:px, Float:py, Float:pz;
                GetPlayerPos(i, px, py, pz);

                if(GetPlayerDistanceFromPoint(i, x, y, z) <= 10.0)
                {
                    // Use different color based on player status
                    if(PlayerInfo[playerid][pJailed] || (PlayerInfo[playerid][pAdminDuty] && PlayerInfo[playerid][pAdminLevel] > 0))
                    {
                        SendClientMessage(i, 0xE6E6FAFF, string); // Light purple for OOC
                    }
                    else
                    {
                        SendClientMessage(i, 0xFFFFFFFF, string); // White for IC
                    }
                }
            }
        }
    }

    return 0; // Prevent default chat
}

public OnPlayerTakeDamage(playerid, issuerid, Float:amount, weaponid, bodypart)
{
    // Prevent damage if player is dead
    if(PlayerInfo[playerid][pIsDead])
    {
        // Restore health to prevent damage
        SetPlayerHealth(playerid, PlayerInfo[playerid][pHealth]);
        return 0; // Block damage
    }

    // Apply body part damage for all damage types
    if(amount > 0.0)
    {
        if(IsPlayerConnected(issuerid) && PlayerInfo[issuerid][pLogged])
        {
            // Combat damage from another player
            ApplyBodyPartDamage(playerid, issuerid, weaponid, bodypart);
        }
        else if(issuerid == INVALID_PLAYER_ID)
        {
            // Fall damage or environmental damage
            ApplyFallDamage(playerid, amount);
        }
    }

    return 1;
}

// Function to apply body part damage with better detection
stock ApplyBodyPartDamage(playerid, issuerid, weaponid, bodypart)
{
    new targetBodyPartIndex = -1;
    new actualBodyPart = bodypart;

    // For melee weapons (fist, bat, etc) or invalid bodypart, use position-based detection
    if(bodypart < 3 || bodypart > 9 || weaponid == 0 || weaponid == 1 || weaponid == 2 || weaponid == 5 || weaponid == 6)
    {
        // Get relative position between attacker and victim to determine hit area
        new Float:attackerX, Float:attackerY, Float:attackerZ;
        new Float:victimX, Float:victimY, Float:victimZ;

        GetPlayerPos(issuerid, attackerX, attackerY, attackerZ);
        GetPlayerPos(playerid, victimX, victimY, victimZ);

        new Float:heightDiff = attackerZ - victimZ;

        // Determine body part based on height difference and distance
        if(heightDiff > 0.5) // Attacker is higher - likely head hit
        {
            actualBodyPart = 9; // Head
        }
        else if(heightDiff < -0.3) // Attacker is lower - likely leg hit
        {
            new rand = random(2);
            actualBodyPart = (rand == 0) ? 7 : 8; // Left or Right Leg
        }
        else // Same level - torso, arms, or groin
        {
            new rand = random(100);
            if(rand < 40) // 40% chance torso
                actualBodyPart = 3;
            else if(rand < 55) // 15% chance groin
                actualBodyPart = 4;
            else if(rand < 77) // 22% chance left arm
                actualBodyPart = 5;
            else // 23% chance right arm
                actualBodyPart = 6;
        }
    }

    // Convert to array index
    if(actualBodyPart >= 3 && actualBodyPart <= 9)
    {
        targetBodyPartIndex = actualBodyPart - 3;

        // Apply bruise to the specific body part
        PlayerInfo[playerid][pBodyPartCondition][targetBodyPartIndex] = CONDITION_BRUISE;

        // Notify target about injury
        new bodyPartName[32], conditionName[16];
        GetBodyPartName(actualBodyPart, bodyPartName);
        GetConditionName(CONDITION_BRUISE, conditionName);

        new injuryMsg[128];
        format(injuryMsg, sizeof(injuryMsg), "INJURY: {FFFFFF}Your {FFFF00}%s {FFFFFF}is now {FF0000}%s{FFFFFF}! Use /myhealth to check.", bodyPartName, conditionName);
        SendClientMessage(playerid, 0xFF8000FF, injuryMsg);

        // Notify attacker
        if(IsPlayerConnected(issuerid) && PlayerInfo[issuerid][pLogged])
        {
            new targetName[MAX_PLAYER_NAME];
            GetPlayerName(playerid, targetName, sizeof(targetName));
            new attackerMsg[128];
            format(attackerMsg, sizeof(attackerMsg), "HIT: {FFFFFF}You injured %s's {FFFF00}%s{FFFFFF}!", targetName, bodyPartName);
            SendClientMessage(issuerid, 0xFF8000FF, attackerMsg);
        }
    }

    return 1;
}

// Function to apply fall damage based on GTA damage amount
stock ApplyFallDamage(playerid, Float:amount)
{
    new injuryType = CONDITION_BRUISE;
    new bodyPartIndex = -1;

    // Determine injury severity based on damage amount
    if(amount >= 50.0) // High fall damage = Broken Bone
    {
        injuryType = CONDITION_BROKEN_BONE;
    }
    else if(amount >= 25.0) // Medium fall damage = Dislocated
    {
        injuryType = CONDITION_DISLOCATED;
    }
    else if(amount >= 10.0) // Low fall damage = Bruise
    {
        injuryType = CONDITION_BRUISE;
    }
    else
    {
        return 1; // Too low damage, no injury
    }

    // Randomly select which body part gets injured (legs more likely for falls)
    new rand = random(100);
    if(rand < 40) // 40% chance - Left Leg
        bodyPartIndex = 4;
    else if(rand < 80) // 40% chance - Right Leg
        bodyPartIndex = 5;
    else if(rand < 90) // 10% chance - Torso
        bodyPartIndex = 0;
    else // 10% chance - Head
        bodyPartIndex = 6;

    // Apply injury
    PlayerInfo[playerid][pBodyPartCondition][bodyPartIndex] = injuryType;

    // Get body part name for notification
    new bodyPartName[32], conditionName[16];
    new bodyPartID;

    // Convert index to SA-MP bodypart ID
    switch(bodyPartIndex)
    {
        case 0: bodyPartID = 3; // Torso
        case 1: bodyPartID = 4; // Groin
        case 2: bodyPartID = 5; // Left Arm
        case 3: bodyPartID = 6; // Right Arm
        case 4: bodyPartID = 7; // Left Leg
        case 5: bodyPartID = 8; // Right Leg
        case 6: bodyPartID = 9; // Head
        default: bodyPartID = 3; // Default to Torso
    }

    GetBodyPartName(bodyPartID, bodyPartName);
    GetConditionName(injuryType, conditionName);

    // Notify player
    new injuryMsg[128];
    format(injuryMsg, sizeof(injuryMsg), "FALL INJURY: {FFFFFF}You took %.1f fall damage! Your {FFFF00}%s {FFFFFF}is now {FF0000}%s{FFFFFF}!", amount, bodyPartName, conditionName);
    SendClientMessage(playerid, 0xFF8000FF, injuryMsg);
    SendClientMessage(playerid, 0xFF8000FF, "Use /myhealth to check your injuries.");

    return 1;
}

// ===== CUSTOM WEAPON SYSTEM FUNCTIONS =====

// Function to get weapon slot from weapon ID
stock GetWeaponSlot(weaponid)
{
    switch(weaponid)
    {
        case 0, 1: return WEAPON_SLOT_FIST;
        case 2..9: return WEAPON_SLOT_MELEE;
        case 22..24: return WEAPON_SLOT_PISTOL;
        case 25..27: return WEAPON_SLOT_SHOTGUN;
        case 28, 29, 32: return WEAPON_SLOT_SMG;
        case 30, 31: return WEAPON_SLOT_ASSAULT;
        case 33, 34: return WEAPON_SLOT_RIFLE;
        case 35..37: return WEAPON_SLOT_HEAVY;
        case 16..18: return WEAPON_SLOT_THROWN;
        case 38, 39: return WEAPON_SLOT_SPECIAL;
        case 14, 15: return WEAPON_SLOT_GIFT;
        case 40: return WEAPON_SLOT_DETONATOR;
        case 41..43: return WEAPON_SLOT_SPRAY;
    }
    return -1;
}

// Function to check if weapon is illegal
stock IsWeaponIllegal(weaponid)
{
    for(new i = 0; i < sizeof(IllegalWeapons); i++)
    {
        if(IllegalWeapons[i] == weaponid)
            return 1;
    }
    return 0;
}

// Function to give custom weapon with ammo
stock GivePlayerCustomWeapon(playerid, weaponid, ammo)
{
    if(!IsPlayerConnected(playerid) || !PlayerInfo[playerid][pLogged])
        return 0;

    new slot = GetWeaponSlot(weaponid);
    if(slot == -1) return 0;

    // Set magazine size
    PlayerInfo[playerid][pWeaponMagazine][slot] = WeaponMagazineSize[weaponid];

    // Set custom ammo
    PlayerInfo[playerid][pWeaponAmmo][slot] = ammo;

    // Add to inventory
    PlayerInfo[playerid][pInventoryWeapons][slot] = weaponid;
    PlayerInfo[playerid][pInventoryAmmoUnits][slot] = (ammo / 100) + ((ammo % 100) > 0 ? 1 : 0); // Convert ammo to units

    // Give weapon with 99999 ammo (hidden)
    GivePlayerWeapon(playerid, weaponid, 99999);

    // Update ammo display if this weapon is currently equipped
    new currentWeapon = GetPlayerWeapon(playerid);
    if(currentWeapon == weaponid)
    {
        UpdateAmmoDisplay(playerid);
    }

    // Save weapons to database
    SavePlayerWeapons(playerid);

    return 1;
}

// Function to update ammo display
stock UpdateAmmoDisplay(playerid)
{
    if(!IsPlayerConnected(playerid) || !PlayerInfo[playerid][pLogged])
        return 0;

    new weaponid = GetPlayerWeapon(playerid);
    new slot = GetWeaponSlot(weaponid);

    if(slot == -1 || WeaponMagazineSize[weaponid] == 0)
    {
        // Hide textdraw for melee weapons or invalid weapons
        if(PlayerInfo[playerid][pAmmoTDShown])
        {
            TextDrawHideForPlayer(playerid, PlayerInfo[playerid][pAmmoTD]);
            PlayerInfo[playerid][pAmmoTDShown] = false;
        }
        return 1;
    }

    // Update textdraw text
    new ammoText[16];
    format(ammoText, sizeof(ammoText), "%d", PlayerInfo[playerid][pWeaponAmmo][slot]);
    TextDrawSetString(PlayerInfo[playerid][pAmmoTD], ammoText);

    // Show textdraw if not shown
    if(!PlayerInfo[playerid][pAmmoTDShown])
    {
        TextDrawShowForPlayer(playerid, PlayerInfo[playerid][pAmmoTD]);
        PlayerInfo[playerid][pAmmoTDShown] = true;
    }

    return 1;
}

// Function to consume ammo when shooting
stock ConsumeAmmo(playerid)
{
    if(!IsPlayerConnected(playerid) || !PlayerInfo[playerid][pLogged])
        return 0;

    new weaponid = GetPlayerWeapon(playerid);
    new slot = GetWeaponSlot(weaponid);

    if(slot == -1 || WeaponMagazineSize[weaponid] == 0)
        return 1; // Melee weapons don't consume ammo

    if(PlayerInfo[playerid][pWeaponAmmo][slot] > 0)
    {
        PlayerInfo[playerid][pWeaponAmmo][slot]--;
        UpdateAmmoDisplay(playerid);

        // If ammo is 0, check if we should remove weapon from inventory
        if(PlayerInfo[playerid][pWeaponAmmo][slot] == 0)
        {
            // Hide ammo display
            if(PlayerInfo[playerid][pAmmoTDShown])
            {
                TextDrawHideForPlayer(playerid, PlayerInfo[playerid][pAmmoTD]);
                PlayerInfo[playerid][pAmmoTDShown] = false;
            }

            // Remove weapon from player's hand but keep in inventory
            new currentWeaponID = PlayerInfo[playerid][pInventoryWeapons][slot];
            SetPlayerAmmo(playerid, currentWeaponID, 0); // Disable weapon by setting GTA ammo to 0

            // Notify player
            SendClientMessage(playerid, 0xFF8000FF, "WEAPON: {FFFFFF}Your weapon is out of ammo and removed from hand! Check /items to reload.");
        }

        return 1;
    }

    return 0; // No ammo left
}

// Function to reload weapon
stock ReloadWeapon(playerid)
{
    if(!IsPlayerConnected(playerid) || !PlayerInfo[playerid][pLogged])
        return 0;

    new weaponid = GetPlayerWeapon(playerid);
    new slot = GetWeaponSlot(weaponid);

    if(slot == -1 || WeaponMagazineSize[weaponid] == 0)
        return 0; // Can't reload melee weapons

    // Check if player has spare ammo (this could be expanded with inventory system)
    // For now, just refill to magazine size
    PlayerInfo[playerid][pWeaponAmmo][slot] = PlayerInfo[playerid][pWeaponMagazine][slot];

    UpdateAmmoDisplay(playerid);

    new weaponName[32];
    GetWeaponName(weaponid, weaponName, sizeof(weaponName));

    new reloadMsg[128];
    format(reloadMsg, sizeof(reloadMsg), "RELOAD: {FFFFFF}You reloaded your %s (%d rounds)", weaponName, PlayerInfo[playerid][pWeaponAmmo][slot]);
    SendClientMessage(playerid, 0x00FF00FF, reloadMsg);

    return 1;
}

// Function to reload weapon from inventory
stock ReloadWeaponFromInventory(playerid, weaponid)
{
    if(!IsPlayerConnected(playerid) || !PlayerInfo[playerid][pLogged])
        return 0;

    new slot = GetWeaponSlot(weaponid);
    if(slot == -1 || WeaponMagazineSize[weaponid] == 0)
        return 0;

    // Check if player has ammo units for this weapon
    if(PlayerInfo[playerid][pInventoryAmmoUnits][slot] <= 0)
    {
        SendClientMessage(playerid, 0xFF0000FF, "RELOAD: {FFFFFF}You don't have any ammo units for this weapon!");
        return 0;
    }

    // Use 1 ammo unit (100 ammo)
    PlayerInfo[playerid][pInventoryAmmoUnits][slot]--;

    // Reload weapon with full magazine
    PlayerInfo[playerid][pWeaponAmmo][slot] = PlayerInfo[playerid][pWeaponMagazine][slot];

    // Give weapon back to player with 99999 GTA ammo (hidden)
    GivePlayerWeapon(playerid, weaponid, 99999);

    // Update ammo display
    UpdateAmmoDisplay(playerid);

    new weaponName[32];
    GetWeaponName(weaponid, weaponName, sizeof(weaponName));

    new reloadMsg[128];
    format(reloadMsg, sizeof(reloadMsg), "RELOAD: {FFFFFF}You reloaded your %s from inventory! Ammo units left: {FFFF00}%d",
        weaponName, PlayerInfo[playerid][pInventoryAmmoUnits][slot]);
    SendClientMessage(playerid, 0x00FF00FF, reloadMsg);

    return 1;
}

// OnPlayerWeaponShot - Handle custom ammo consumption
public OnPlayerWeaponShot(playerid, weaponid, hittype, hitid, Float:fX, Float:fY, Float:fZ)
{
    // Consume ammo when shooting
    new slot = GetWeaponSlot(weaponid);
    if(slot != -1 && WeaponMagazineSize[weaponid] > 0)
    {
        if(PlayerInfo[playerid][pWeaponAmmo][slot] <= 0)
        {
            // No ammo left, prevent shooting
            SendClientMessage(playerid, 0xFF0000FF, "WEAPON: {FFFFFF}No ammo! Your weapon is decoration only.");
            return 0; // Block the shot
        }

        // Consume ammo
        ConsumeAmmo(playerid);
    }

    return 1;
}

// OnPlayerKeyStateChange - Handle weapon switching and reloading
public OnPlayerKeyStateChange(playerid, newkeys, oldkeys)
{
    // Handle weapon switching
    if(newkeys != oldkeys)
    {
        new currentWeapon = GetPlayerWeapon(playerid);
        if(currentWeapon != PlayerInfo[playerid][pCurrentWeapon])
        {
            PlayerInfo[playerid][pCurrentWeapon] = currentWeapon;
            UpdateAmmoDisplay(playerid);
        }
    }

    // Handle reload key (R key)
    if(newkeys & KEY_CROUCH && !(oldkeys & KEY_CROUCH))
    {
        // Check if player is aiming (right click) + R = reload
        if(newkeys & KEY_HANDBRAKE)
        {
            ReloadWeapon(playerid);
        }
    }

    return 1;
}

public OnPlayerCommandText(playerid, cmdtext[])
{
    // Log command usage to server console
    if(PlayerInfo[playerid][pLogged])
    {
        new playerName[MAX_PLAYER_NAME], logMessage[256];
        GetPlayerName(playerid, playerName, sizeof(playerName));

        // Get current time for logging
        new year, month, day, hour, minute, second;
        getdate(year, month, day);
        gettime(hour, minute, second);

        // Format log message with timestamp
        format(logMessage, sizeof(logMessage),
            "[%02d:%02d:%02d] COMMAND: %s used command: %s",
            hour, minute, second, playerName, cmdtext);

        // Print to server console (samp-server.exe)
        print(logMessage);

        // Also log to server_log.txt if needed
        printf("[COMMAND] %s used: %s", playerName, cmdtext);
    }

    // Return 0 to let ZCMD handle the command
    return 0;
}



stock SavePlayerData(playerid)
{
    if(!PlayerInfo[playerid][pLogged]) return 0;
    
    GetPlayerPos(playerid, PlayerInfo[playerid][pPosX], PlayerInfo[playerid][pPosY], PlayerInfo[playerid][pPosZ]);
    PlayerInfo[playerid][pMoney] = GetPlayerMoney(playerid);

    // Update playing time
    if(PlayerInfo[playerid][pLoginTime] > 0)
    {
        new sessionTime = gettime() - PlayerInfo[playerid][pLoginTime];
        PlayerInfo[playerid][pPlayingTime] += sessionTime;
        CheckPlayerLevel(playerid); // Check for level up
    }

    // Get current angle before saving
    GetPlayerFacingAngle(playerid, PlayerInfo[playerid][pAngle]);

    // Get current health and armour
    GetPlayerHealth(playerid, PlayerInfo[playerid][pHealth]);
    GetPlayerArmour(playerid, PlayerInfo[playerid][pArmour]);

    // Convert body part data to strings
    new bodyPartConditionsStr[32];
    BodyPartConditionsToString(playerid, bodyPartConditionsStr);

    new query[1536];
    mysql_format(g_SQL, query, sizeof(query),
        "UPDATE `players` SET `level` = %d, `money` = %d, `pos_x` = %f, `pos_y` = %f, `pos_z` = %f, `angle` = %f, `skin` = %d, `spawn_location` = %d, `playing_time` = %d, `last_login` = %d, `admin_level` = %d, `admin_name` = '%e', `total_duty_time` = %d, `health` = %f, `armour` = %f, `max_health` = %f, `warn_count` = %d, `gender` = %d, `origin` = '%e', `interior` = %d, `virtual_world` = %d, `jailed` = %d, `jail_time` = %d, `banned` = %d, `ban_time` = %d, `ban_reason` = '%e', `hunger` = %d, `bladder` = %d, `energy` = %d, `hbe_mode` = %d, `body_part_conditions` = '%e' WHERE `id` = %d",
        PlayerInfo[playerid][pLevel], PlayerInfo[playerid][pMoney],
        PlayerInfo[playerid][pPosX], PlayerInfo[playerid][pPosY], PlayerInfo[playerid][pPosZ], PlayerInfo[playerid][pAngle],
        PlayerInfo[playerid][pSkin], PlayerInfo[playerid][pSpawnLocation], PlayerInfo[playerid][pPlayingTime],
        PlayerInfo[playerid][pLoginTime], PlayerInfo[playerid][pAdminLevel], PlayerInfo[playerid][pAdminName], PlayerInfo[playerid][pTotalDutyTime],
        PlayerInfo[playerid][pHealth], PlayerInfo[playerid][pArmour], PlayerInfo[playerid][pMaxHealth], PlayerInfo[playerid][pWarnCount], PlayerInfo[playerid][pGender], PlayerInfo[playerid][pOrigin], PlayerInfo[playerid][pInterior], PlayerInfo[playerid][pVirtualWorld], PlayerInfo[playerid][pJailed] ? 1 : 0, PlayerInfo[playerid][pJailTime], PlayerInfo[playerid][pBanned] ? 1 : 0, PlayerInfo[playerid][pBanTime], PlayerInfo[playerid][pBanReason], PlayerInfo[playerid][pHunger], PlayerInfo[playerid][pBladder], PlayerInfo[playerid][pEnergy], PlayerInfo[playerid][pHBEMode], bodyPartConditionsStr, PlayerInfo[playerid][pID]);
    mysql_tquery(g_SQL, query);
    

    // Save player weapons to database
    SavePlayerWeapons(playerid);

    return 1;
}

// ========== WEAPON DATABASE FUNCTIONS ==========

// Function to save player weapons to database
stock SavePlayerWeapons(playerid)
{
    if(!PlayerInfo[playerid][pLogged]) return 0;

    // Clear existing weapons for this player
    new clear_query[128];
    mysql_format(g_SQL, clear_query, sizeof(clear_query),
        "DELETE FROM `player_weapon` WHERE `player_id` = %d",
        PlayerInfo[playerid][pID]
    );
    mysql_tquery(g_SQL, clear_query);

    // Save current weapons
    for(new slot = 0; slot < 13; slot++)
    {
        if(PlayerInfo[playerid][pInventoryWeapons][slot] > 0)
        {
            new insert_query[256];
            mysql_format(g_SQL, insert_query, sizeof(insert_query),
                "INSERT INTO `player_weapon` (`player_id`, `weapon_slot`, `weapon_id`, `ammo`, `magazine_size`) VALUES (%d, %d, %d, %d, %d)",
                PlayerInfo[playerid][pID],
                slot,
                PlayerInfo[playerid][pInventoryWeapons][slot],
                PlayerInfo[playerid][pWeaponAmmo][slot],
                PlayerInfo[playerid][pWeaponMagazine][slot]
            );
            mysql_tquery(g_SQL, insert_query);
        }
    }

    return 1;
}

// Function to load player weapons from database
stock LoadPlayerWeapons(playerid)
{
    if(!PlayerInfo[playerid][pLogged]) return 0;

    new query[256];
    mysql_format(g_SQL, query, sizeof(query),
        "SELECT `weapon_slot`, `weapon_id`, `ammo`, `magazine_size` FROM `player_weapon` WHERE `player_id` = %d ORDER BY `weapon_slot`",
        PlayerInfo[playerid][pID]
    );
    mysql_tquery(g_SQL, query, "OnLoadPlayerWeapons", "i", playerid);

    return 1;
}

// Callback for loading player weapons
forward OnLoadPlayerWeapons(playerid);
public OnLoadPlayerWeapons(playerid)
{
    if(!IsPlayerConnected(playerid) || !PlayerInfo[playerid][pLogged]) return 0;

    // Clear current weapons first
    ResetPlayerWeapons(playerid);
    for(new i = 0; i < 13; i++)
    {
        PlayerInfo[playerid][pInventoryWeapons][i] = 0;
        PlayerInfo[playerid][pWeaponAmmo][i] = 0;
        PlayerInfo[playerid][pWeaponMagazine][i] = 0;
    }

    new rows = cache_num_rows();
    if(rows > 0)
    {
        for(new i = 0; i < rows; i++)
        {
            new slot, weaponid, ammo, magazine;
            cache_get_value_int(i, "weapon_slot", slot);
            cache_get_value_int(i, "weapon_id", weaponid);
            cache_get_value_int(i, "ammo", ammo);
            cache_get_value_int(i, "magazine_size", magazine);

            if(slot >= 0 && slot < 13)
            {
                PlayerInfo[playerid][pInventoryWeapons][slot] = weaponid;
                PlayerInfo[playerid][pWeaponAmmo][slot] = ammo;
                PlayerInfo[playerid][pWeaponMagazine][slot] = magazine;

                // Give weapon with hidden ammo
                GivePlayerWeapon(playerid, weaponid, 99999);
            }
        }

        // Update ammo display for current weapon
        UpdateAmmoDisplay(playerid);
    }

    return 1;
}

// ========== INVENTORY SYSTEM FUNCTIONS ==========

// Function to add item to player inventory
stock AddPlayerInventoryItem(playerid, const itemType[], const itemName[], quantity = 1, const itemData[] = "")
{
    if(!PlayerInfo[playerid][pLogged]) return 0;

    // Check if item already exists
    new check_query[256];
    mysql_format(g_SQL, check_query, sizeof(check_query),
        "SELECT `id`, `quantity` FROM `player_inventory` WHERE `player_id` = %d AND `item_type` = '%e' AND `item_name` = '%e' LIMIT 1",
        PlayerInfo[playerid][pID], itemType, itemName
    );

    new Cache:result = mysql_query(g_SQL, check_query);
    new rows = cache_num_rows();

    if(rows > 0)
    {
        // Item exists, update quantity
        new existing_id, existing_quantity;
        cache_get_value_int(0, "id", existing_id);
        cache_get_value_int(0, "quantity", existing_quantity);

        new update_query[256];
        mysql_format(g_SQL, update_query, sizeof(update_query),
            "UPDATE `player_inventory` SET `quantity` = %d WHERE `id` = %d",
            existing_quantity + quantity, existing_id
        );
        mysql_tquery(g_SQL, update_query);
    }
    else
    {
        // Item doesn't exist, insert new
        new insert_query[512];
        mysql_format(g_SQL, insert_query, sizeof(insert_query),
            "INSERT INTO `player_inventory` (`player_id`, `item_type`, `item_name`, `quantity`, `item_data`) VALUES (%d, '%e', '%e', %d, '%e')",
            PlayerInfo[playerid][pID], itemType, itemName, quantity, itemData
        );
        mysql_tquery(g_SQL, insert_query);
    }

    cache_delete(result);
    return 1;
}

// Function to remove item from player inventory
stock RemovePlayerInventoryItem(playerid, const itemType[], const itemName[], quantity = 1)
{
    if(!PlayerInfo[playerid][pLogged]) return 0;

    new query[256];
    mysql_format(g_SQL, query, sizeof(query),
        "SELECT `id`, `quantity` FROM `player_inventory` WHERE `player_id` = %d AND `item_type` = '%e' AND `item_name` = '%e' LIMIT 1",
        PlayerInfo[playerid][pID], itemType, itemName
    );

    new Cache:result = mysql_query(g_SQL, query);
    new rows = cache_num_rows();

    if(rows > 0)
    {
        new item_id, current_quantity;
        cache_get_value_int(0, "id", item_id);
        cache_get_value_int(0, "quantity", current_quantity);

        if(current_quantity <= quantity)
        {
            // Remove item completely
            new delete_query[128];
            mysql_format(g_SQL, delete_query, sizeof(delete_query),
                "DELETE FROM `player_inventory` WHERE `id` = %d",
                item_id
            );
            mysql_tquery(g_SQL, delete_query);
        }
        else
        {
            // Reduce quantity
            new update_query[256];
            mysql_format(g_SQL, update_query, sizeof(update_query),
                "UPDATE `player_inventory` SET `quantity` = %d WHERE `id` = %d",
                current_quantity - quantity, item_id
            );
            mysql_tquery(g_SQL, update_query);
        }

        cache_delete(result);
        return 1;
    }

    cache_delete(result);
    return 0;
}

// Function to get player inventory item count
stock GetPlayerInventoryItemCount(playerid, const itemType[], const itemName[])
{
    if(!PlayerInfo[playerid][pLogged]) return 0;

    new query[256];
    mysql_format(g_SQL, query, sizeof(query),
        "SELECT `quantity` FROM `player_inventory` WHERE `player_id` = %d AND `item_type` = '%e' AND `item_name` = '%e' LIMIT 1",
        PlayerInfo[playerid][pID], itemType, itemName
    );

    new Cache:result = mysql_query(g_SQL, query);
    new rows = cache_num_rows();
    new quantity = 0;

    if(rows > 0)
    {
        cache_get_value_int(0, "quantity", quantity);
    }

    cache_delete(result);
    return quantity;
}

// Function to format numbers with commas
stock FormatNumber(number)
{
    new string[32], temp[32];
    format(temp, sizeof(temp), "%d", number);

    new len = strlen(temp);
    new pos = 0;

    for(new i = len - 1; i >= 0; i--)
    {
        string[pos] = temp[i];
        pos++;

        if((len - i) % 3 == 0 && i != 0)
        {
            string[pos] = ',';
            pos++;
        }
    }

    // Reverse the string
    new result[32];
    for(new i = 0; i < pos; i++)
    {
        result[i] = string[pos - 1 - i];
    }
    result[pos] = '\0';

    return result;
}

// Command untuk save data manual
CMD:save(playerid, params[])
{
    if(!PlayerInfo[playerid][pLogged])
    {
        new error_msg[] = "Anda harus login terlebih dahulu!";
        SendClientMessage(playerid, 0xFF0000FF, error_msg);
        return 1;
    }
    
    SavePlayerData(playerid);
    new success_msg[] = "Data Anda telah disimpan!";
    SendClientMessage(playerid, 0x00FF00FF, success_msg);
    return 1;
}

// ========== ADMIN COMMANDS ==========

// Admin command to give items to players (Dialog Version)
CMD:agiveitem(playerid, params[])
{
    if(PlayerInfo[playerid][pAdminLevel] < 10)
    {
        SendClientMessage(playerid, COLOR_RED, "ERROR: Anda memerlukan admin level 10 untuk menggunakan command ini!");
        return 1;
    }

    new targetid;
    if(sscanf(params, "u", targetid))
    {
        SendClientMessage(playerid, COLOR_YELLOW, "USAGE: /agiveitem [playerid]");
        return 1;
    }

    if(!IsPlayerConnected(targetid))
    {
        SendClientMessage(playerid, COLOR_RED, "ERROR: Player tidak ditemukan!");
        return 1;
    }

    if(!PlayerInfo[targetid][pLogged])
    {
        SendClientMessage(playerid, COLOR_RED, "ERROR: Player target belum login!");
        return 1;
    }

    // Store target ID for dialog callbacks
    SetPVarInt(playerid, "GiveItemTarget", targetid);

    // Show main item type selection dialog
    new targetName[MAX_PLAYER_NAME];
    GetPlayerName(targetid, targetName, sizeof(targetName));

    new dialogTitle[64];
    format(dialogTitle, sizeof(dialogTitle), "Give Item to %s", targetName);

    Dialog_Show(playerid, GiveItemMain, DIALOG_STYLE_LIST, dialogTitle,
        "Money\nWeapon\nMagazine\nComponent\nBait",
        "Select", "Cancel");

    return 1;
}

// ========== GIVE ITEM DIALOG HANDLERS ==========

// Main give item dialog
Dialog:GiveItemMain(playerid, response, listitem, inputtext[])
{
    if(!response)
    {
        DeletePVar(playerid, "GiveItemTarget");
        return 1;
    }

    new targetid = GetPVarInt(playerid, "GiveItemTarget");
    if(!IsPlayerConnected(targetid) || !PlayerInfo[targetid][pLogged])
    {
        SendClientMessage(playerid, COLOR_RED, "ERROR: Target player tidak tersedia!");
        DeletePVar(playerid, "GiveItemTarget");
        return 1;
    }

    switch(listitem)
    {
        case 0: // Money
        {
            Dialog_Show(playerid, GiveItemAmount, DIALOG_STYLE_INPUT, "Give Money",
                "Masukkan jumlah uang yang ingin diberikan:\n(1 - 10,000,000)",
                "Give", "Back");
            SetPVarString(playerid, "GiveItemType", "money");
        }
        case 1: // Weapon
        {
            Dialog_Show(playerid, GiveItemWeapon, DIALOG_STYLE_LIST, "Select Weapon",
                "Desert Eagle (.50)\nM4A1\nAK-47\nMP5\nShotgun\nSniper Rifle\nRocket Launcher\nUzi\nTec-9\nCountry Rifle\nCombat Shotgun",
                "Select", "Back");
        }
        case 2: // Magazine
        {
            Dialog_Show(playerid, GiveItemAmount, DIALOG_STYLE_INPUT, "Give Magazine",
                "Masukkan jumlah magazine yang ingin diberikan:\n(1 - 100)",
                "Give", "Back");
            SetPVarString(playerid, "GiveItemType", "magazine");
        }
        case 3: // Component
        {
            Dialog_Show(playerid, GiveItemComponent, DIALOG_STYLE_LIST, "Select Component",
                "Silencer\nLaser Sight\nScope\nExtended Clip\nFlashlight",
                "Select", "Back");
        }
        case 4: // Bait
        {
            Dialog_Show(playerid, GiveItemBait, DIALOG_STYLE_LIST, "Select Bait",
                "Worm\nCorn\nBread\nCheese\nFish Bait",
                "Select", "Back");
        }
    }
    return 1;
}

// Weapon selection dialog
Dialog:GiveItemWeapon(playerid, response, listitem, inputtext[])
{
    if(!response)
    {
        // Back to main menu
        new targetid = GetPVarInt(playerid, "GiveItemTarget");
        new targetName[MAX_PLAYER_NAME];
        GetPlayerName(targetid, targetName, sizeof(targetName));

        new dialogTitle[64];
        format(dialogTitle, sizeof(dialogTitle), "Give Item to %s", targetName);

        Dialog_Show(playerid, GiveItemMain, DIALOG_STYLE_LIST, dialogTitle,
            "Money\nWeapon\nMagazine\nComponent\nBait",
            "Select", "Cancel");
        return 1;
    }

    new targetid = GetPVarInt(playerid, "GiveItemTarget");
    if(!IsPlayerConnected(targetid) || !PlayerInfo[targetid][pLogged])
    {
        SendClientMessage(playerid, COLOR_RED, "ERROR: Target player tidak tersedia!");
        DeletePVar(playerid, "GiveItemTarget");
        return 1;
    }

    new weaponid;
    new weaponName[32];
    switch(listitem)
    {
        case 0:
        {
            weaponid = 24;
            format(weaponName, sizeof(weaponName), "Desert Eagle");
        }
        case 1:
        {
            weaponid = 31;
            format(weaponName, sizeof(weaponName), "M4A1");
        }
        case 2:
        {
            weaponid = 30;
            format(weaponName, sizeof(weaponName), "AK-47");
        }
        case 3:
        {
            weaponid = 29;
            format(weaponName, sizeof(weaponName), "MP5");
        }
        case 4:
        {
            weaponid = 25;
            format(weaponName, sizeof(weaponName), "Shotgun");
        }
        case 5:
        {
            weaponid = 34;
            format(weaponName, sizeof(weaponName), "Sniper Rifle");
        }
        case 6:
        {
            weaponid = 35;
            format(weaponName, sizeof(weaponName), "Rocket Launcher");
        }
        case 7:
        {
            weaponid = 28;
            format(weaponName, sizeof(weaponName), "Uzi");
        }
        case 8:
        {
            weaponid = 32;
            format(weaponName, sizeof(weaponName), "Tec-9");
        }
        case 9:
        {
            weaponid = 33;
            format(weaponName, sizeof(weaponName), "Country Rifle");
        }
        case 10:
        {
            weaponid = 27;
            format(weaponName, sizeof(weaponName), "Combat Shotgun");
        }
    }

    // Give weapon with 100 ammo automatically
    GivePlayerCustomWeapon(targetid, weaponid, 100);

    // Send messages
    new adminName[MAX_PLAYER_NAME], targetName[MAX_PLAYER_NAME];
    GetPlayerName(playerid, adminName, sizeof(adminName));
    GetPlayerName(targetid, targetName, sizeof(targetName));

    new adminMsg[128], targetMsg[128];
    format(adminMsg, sizeof(adminMsg), "ADMIN: Anda memberikan %s (100 ammo) kepada %s", weaponName, targetName);
    format(targetMsg, sizeof(targetMsg), "ADMIN: Anda menerima %s (100 ammo) dari admin %s", weaponName, adminName);

    SendClientMessage(playerid, COLOR_GREEN, adminMsg);
    SendClientMessage(targetid, COLOR_GREEN, targetMsg);

    // Log admin action
    printf("[ADMIN] %s gave %s (100 ammo) to %s", adminName, weaponName, targetName);

    DeletePVar(playerid, "GiveItemTarget");
    return 1;
}

// Component selection dialog
Dialog:GiveItemComponent(playerid, response, listitem, inputtext[])
{
    if(!response)
    {
        // Back to main menu
        new targetid = GetPVarInt(playerid, "GiveItemTarget");
        new params[16];
        format(params, sizeof(params), "%d", targetid);
        CMD:agiveitem(playerid, params);
        return 1;
    }

    new componentName[32];
    switch(listitem)
    {
        case 0: format(componentName, sizeof(componentName), "Silencer");
        case 1: format(componentName, sizeof(componentName), "Laser Sight");
        case 2: format(componentName, sizeof(componentName), "Scope");
        case 3: format(componentName, sizeof(componentName), "Extended Clip");
        case 4: format(componentName, sizeof(componentName), "Flashlight");
    }

    SetPVarString(playerid, "GiveItemType", "component");
    SetPVarString(playerid, "GiveItemName", componentName);

    new dialogText[128];
    format(dialogText, sizeof(dialogText), "Masukkan jumlah %s yang ingin diberikan:\n(1 - 50)", componentName);

    Dialog_Show(playerid, GiveItemAmount, DIALOG_STYLE_INPUT, "Give Component Amount",
        dialogText, "Give", "Back");

    return 1;
}

// Bait selection dialog
Dialog:GiveItemBait(playerid, response, listitem, inputtext[])
{
    if(!response)
    {
        // Back to main menu
        new targetid = GetPVarInt(playerid, "GiveItemTarget");
        new targetName[MAX_PLAYER_NAME];
        GetPlayerName(targetid, targetName, sizeof(targetName));

        new dialogTitle[64];
        format(dialogTitle, sizeof(dialogTitle), "Give Item to %s", targetName);

        Dialog_Show(playerid, GiveItemMain, DIALOG_STYLE_LIST, dialogTitle,
            "Money\nWeapon\nMagazine\nComponent\nBait",
            "Select", "Cancel");
        return 1;
    }

    new baitName[32];
    switch(listitem)
    {
        case 0: format(baitName, sizeof(baitName), "Worm");
        case 1: format(baitName, sizeof(baitName), "Corn");
        case 2: format(baitName, sizeof(baitName), "Bread");
        case 3: format(baitName, sizeof(baitName), "Cheese");
        case 4: format(baitName, sizeof(baitName), "Fish Bait");
    }

    SetPVarString(playerid, "GiveItemType", "bait");
    SetPVarString(playerid, "GiveItemName", baitName);

    new dialogText[128];
    format(dialogText, sizeof(dialogText), "Masukkan jumlah %s yang ingin diberikan:\n(1 - 100)", baitName);

    Dialog_Show(playerid, GiveItemAmount, DIALOG_STYLE_INPUT, "Give Bait Amount",
        dialogText, "Give", "Back");

    return 1;
}

// Amount input dialog (for money, magazine, component, bait)
Dialog:GiveItemAmount(playerid, response, listitem, inputtext[])
{
    if(!response)
    {
        // Back to main menu
        new targetid = GetPVarInt(playerid, "GiveItemTarget");
        new params[16];
        format(params, sizeof(params), "%d", targetid);
        CMD:agiveitem(playerid, params);
        return 1;
    }

    new targetid = GetPVarInt(playerid, "GiveItemTarget");
    if(!IsPlayerConnected(targetid) || !PlayerInfo[targetid][pLogged])
    {
        SendClientMessage(playerid, COLOR_RED, "ERROR: Target player tidak tersedia!");
        DeletePVar(playerid, "GiveItemTarget");
        return 1;
    }

    new amount = strval(inputtext);
    if(amount <= 0)
    {
        SendClientMessage(playerid, COLOR_RED, "ERROR: Jumlah harus lebih dari 0!");
        return 1;
    }

    new itemType[32], itemName[64];
    GetPVarString(playerid, "GiveItemType", itemType, sizeof(itemType));

    new adminName[MAX_PLAYER_NAME], targetName[MAX_PLAYER_NAME];
    GetPlayerName(playerid, adminName, sizeof(adminName));
    GetPlayerName(targetid, targetName, sizeof(targetName));

    if(!strcmp(itemType, "money", true))
    {
        if(amount > 10000000)
        {
            SendClientMessage(playerid, COLOR_RED, "ERROR: Maksimal $10,000,000!");
            return 1;
        }

        GivePlayerMoney(targetid, amount);
        PlayerInfo[targetid][pMoney] += amount;

        new adminMsg[128], targetMsg[128];
        format(adminMsg, sizeof(adminMsg), "ADMIN: Anda memberikan $%s kepada %s", FormatNumber(amount), targetName);
        format(targetMsg, sizeof(targetMsg), "ADMIN: Anda menerima $%s dari admin %s", FormatNumber(amount), adminName);

        SendClientMessage(playerid, COLOR_GREEN, adminMsg);
        SendClientMessage(targetid, COLOR_GREEN, targetMsg);

        printf("[ADMIN] %s gave $%d to %s", adminName, amount, targetName);
    }
    else if(!strcmp(itemType, "magazine", true))
    {
        if(amount > 100)
        {
            SendClientMessage(playerid, COLOR_RED, "ERROR: Maksimal 100 magazine!");
            return 1;
        }

        AddPlayerInventoryItem(targetid, "magazine", "Magazine", amount, "");

        new adminMsg[128], targetMsg[128];
        format(adminMsg, sizeof(adminMsg), "ADMIN: Anda memberikan %dx Magazine kepada %s", amount, targetName);
        format(targetMsg, sizeof(targetMsg), "ADMIN: Anda menerima %dx Magazine dari admin %s", amount, adminName);

        SendClientMessage(playerid, COLOR_GREEN, adminMsg);
        SendClientMessage(targetid, COLOR_GREEN, targetMsg);

        printf("[ADMIN] %s gave %dx Magazine to %s", adminName, amount, targetName);
    }
    else
    {
        GetPVarString(playerid, "GiveItemName", itemName, sizeof(itemName));

        new maxAmount = 50;
        if(!strcmp(itemType, "bait", true)) maxAmount = 100;

        if(amount > maxAmount)
        {
            new errorMsg[64];
            format(errorMsg, sizeof(errorMsg), "ERROR: Maksimal %d %s!", maxAmount, itemName);
            SendClientMessage(playerid, COLOR_RED, errorMsg);
            return 1;
        }

        AddPlayerInventoryItem(targetid, itemType, itemName, amount, "");

        new adminMsg[128], targetMsg[128];
        format(adminMsg, sizeof(adminMsg), "ADMIN: Anda memberikan %dx %s kepada %s", amount, itemName, targetName);
        format(targetMsg, sizeof(targetMsg), "ADMIN: Anda menerima %dx %s dari admin %s", amount, itemName, adminName);

        SendClientMessage(playerid, COLOR_GREEN, adminMsg);
        SendClientMessage(targetid, COLOR_GREEN, targetMsg);

        printf("[ADMIN] %s gave %dx %s (%s) to %s", adminName, amount, itemName, itemType, targetName);
    }

    // Clean up PVars
    DeletePVar(playerid, "GiveItemTarget");
    DeletePVar(playerid, "GiveItemType");
    DeletePVar(playerid, "GiveItemName");

    return 1;
}

// Admin command to check player inventory
CMD:acheckitem(playerid, params[])
{
    if(PlayerInfo[playerid][pAdminLevel] < 5)
    {
        SendClientMessage(playerid, COLOR_RED, "ERROR: Anda memerlukan admin level 5 untuk menggunakan command ini!");
        return 1;
    }

    new targetid;
    if(sscanf(params, "u", targetid))
    {
        SendClientMessage(playerid, COLOR_YELLOW, "USAGE: /acheckitem [playerid]");
        return 1;
    }

    if(!IsPlayerConnected(targetid))
    {
        SendClientMessage(playerid, COLOR_RED, "ERROR: Player tidak ditemukan!");
        return 1;
    }

    if(!PlayerInfo[targetid][pLogged])
    {
        SendClientMessage(playerid, COLOR_RED, "ERROR: Player target belum login!");
        return 1;
    }

    new targetName[MAX_PLAYER_NAME];
    GetPlayerName(targetid, targetName, sizeof(targetName));

    new query[256];
    mysql_format(g_SQL, query, sizeof(query),
        "SELECT `item_type`, `item_name`, `quantity`, `item_data` FROM `player_inventory` WHERE `player_id` = %d ORDER BY `item_type`, `item_name`",
        PlayerInfo[targetid][pID]
    );
    mysql_tquery(g_SQL, query, "OnAdminCheckInventory", "ii", playerid, targetid);

    return 1;
}

// Callback for admin inventory check
forward OnAdminCheckInventory(adminid, targetid);
public OnAdminCheckInventory(adminid, targetid)
{
    if(!IsPlayerConnected(adminid) || !IsPlayerConnected(targetid)) return 0;

    new targetName[MAX_PLAYER_NAME];
    GetPlayerName(targetid, targetName, sizeof(targetName));

    new rows = cache_num_rows();
    if(rows == 0)
    {
        new msg[128];
        format(msg, sizeof(msg), "ADMIN: %s tidak memiliki item di inventory", targetName);
        SendClientMessage(adminid, COLOR_YELLOW, msg);
        return 1;
    }

    new msg[256];
    format(msg, sizeof(msg), "=== INVENTORY %s ===", targetName);
    SendClientMessage(adminid, COLOR_YELLOW, msg);

    for(new i = 0; i < rows; i++)
    {
        new itemType[32], itemName[64], quantity, itemData[128];
        cache_get_value(i, "item_type", itemType, sizeof(itemType));
        cache_get_value(i, "item_name", itemName, sizeof(itemName));
        cache_get_value_int(i, "quantity", quantity);
        cache_get_value(i, "item_data", itemData, sizeof(itemData));

        if(strlen(itemData) > 0)
        {
            format(msg, sizeof(msg), "%s: %s x%d [%s]", itemType, itemName, quantity, itemData);
        }
        else
        {
            format(msg, sizeof(msg), "%s: %s x%d", itemType, itemName, quantity);
        }
        SendClientMessage(adminid, COLOR_WHITE, msg);
    }

    return 1;
}

// Player command to check their own inventory
CMD:inventory(playerid, params[])
{
    if(!PlayerInfo[playerid][pLogged])
    {
        SendClientMessage(playerid, COLOR_RED, "ERROR: Anda harus login terlebih dahulu!");
        return 1;
    }

    new query[256];
    mysql_format(g_SQL, query, sizeof(query),
        "SELECT `item_type`, `item_name`, `quantity`, `item_data` FROM `player_inventory` WHERE `player_id` = %d ORDER BY `item_type`, `item_name`",
        PlayerInfo[playerid][pID]
    );
    mysql_tquery(g_SQL, query, "OnShowPlayerInventory", "i", playerid);

    return 1;
}

// Callback for showing player inventory
forward OnShowPlayerInventory(playerid);
public OnShowPlayerInventory(playerid)
{
    if(!IsPlayerConnected(playerid) || !PlayerInfo[playerid][pLogged]) return 0;

    new rows = cache_num_rows();
    if(rows == 0)
    {
        SendClientMessage(playerid, COLOR_YELLOW, "=== INVENTORY ANDA ===");
        SendClientMessage(playerid, COLOR_WHITE, "Inventory Anda kosong.");

        // Show weapons in inventory
        new hasWeapons = 0;
        for(new slot = 0; slot < 13; slot++)
        {
            if(PlayerInfo[playerid][pInventoryWeapons][slot] > 0)
            {
                if(!hasWeapons)
                {
                    SendClientMessage(playerid, COLOR_YELLOW, "=== SENJATA ===");
                    hasWeapons = 1;
                }

                new weaponName[32];
                GetWeaponName(PlayerInfo[playerid][pInventoryWeapons][slot], weaponName, sizeof(weaponName));

                new msg[128];
                format(msg, sizeof(msg), "Slot %d: %s - Ammo: %d/%d",
                    slot, weaponName,
                    PlayerInfo[playerid][pWeaponAmmo][slot],
                    PlayerInfo[playerid][pWeaponMagazine][slot]
                );
                SendClientMessage(playerid, COLOR_WHITE, msg);
            }
        }

        if(!hasWeapons)
        {
            SendClientMessage(playerid, COLOR_WHITE, "Tidak ada senjata di inventory.");
        }

        return 1;
    }

    SendClientMessage(playerid, COLOR_YELLOW, "=== INVENTORY ANDA ===");

    for(new i = 0; i < rows; i++)
    {
        new itemType[32], itemName[64], quantity, itemData[128];
        cache_get_value(i, "item_type", itemType, sizeof(itemType));
        cache_get_value(i, "item_name", itemName, sizeof(itemName));
        cache_get_value_int(i, "quantity", quantity);
        cache_get_value(i, "item_data", itemData, sizeof(itemData));

        new msg[256];
        if(strlen(itemData) > 0)
        {
            format(msg, sizeof(msg), "%s: %s x%d [%s]", itemType, itemName, quantity, itemData);
        }
        else
        {
            format(msg, sizeof(msg), "%s: %s x%d", itemType, itemName, quantity);
        }
        SendClientMessage(playerid, COLOR_WHITE, msg);
    }

    // Show weapons in inventory
    new hasWeapons = 0;
    for(new slot = 0; slot < 13; slot++)
    {
        if(PlayerInfo[playerid][pInventoryWeapons][slot] > 0)
        {
            if(!hasWeapons)
            {
                SendClientMessage(playerid, COLOR_YELLOW, "=== SENJATA ===");
                hasWeapons = 1;
            }

            new weaponName[32];
            GetWeaponName(PlayerInfo[playerid][pInventoryWeapons][slot], weaponName, sizeof(weaponName));

            new msg[128];
            format(msg, sizeof(msg), "Slot %d: %s - Ammo: %d/%d",
                slot, weaponName,
                PlayerInfo[playerid][pWeaponAmmo][slot],
                PlayerInfo[playerid][pWeaponMagazine][slot]
            );
            SendClientMessage(playerid, COLOR_WHITE, msg);
        }
    }

    return 1;
}

// Command to use inventory items
CMD:useitem(playerid, params[])
{
    if(!PlayerInfo[playerid][pLogged])
    {
        SendClientMessage(playerid, COLOR_RED, "ERROR: Anda harus login terlebih dahulu!");
        return 1;
    }

    new itemType[32], itemName[64];
    if(sscanf(params, "s[32]s[64]", itemType, itemName))
    {
        SendClientMessage(playerid, COLOR_YELLOW, "USAGE: /useitem [item_type] [item_name]");
        SendClientMessage(playerid, COLOR_YELLOW, "CONTOH: /useitem component Silencer");
        SendClientMessage(playerid, COLOR_YELLOW, "CONTOH: /useitem bait Worm");
        return 1;
    }

    // Check if player has the item
    new itemCount = GetPlayerInventoryItemCount(playerid, itemType, itemName);
    if(itemCount <= 0)
    {
        SendClientMessage(playerid, COLOR_RED, "ERROR: Anda tidak memiliki item tersebut!");
        return 1;
    }

    // Handle different item types
    if(!strcmp(itemType, "component", true))
    {
        // Handle weapon components
        if(!strcmp(itemName, "Silencer", true))
        {
            new weaponid = GetPlayerWeapon(playerid);
            if(weaponid == 22 || weaponid == 23 || weaponid == 24) // Pistols that can use silencer
            {
                RemovePlayerInventoryItem(playerid, itemType, itemName, 1);
                SendClientMessage(playerid, COLOR_GREEN, "COMPONENT: Silencer telah dipasang pada senjata Anda!");
                // Here you could add actual silencer effect
            }
            else
            {
                SendClientMessage(playerid, COLOR_RED, "ERROR: Senjata ini tidak bisa menggunakan silencer!");
            }
        }
        else
        {
            SendClientMessage(playerid, COLOR_RED, "ERROR: Component tidak dikenal!");
        }
    }
    else if(!strcmp(itemType, "bait", true))
    {
        // Handle fishing baits
        if(!strcmp(itemName, "Worm", true))
        {
            RemovePlayerInventoryItem(playerid, itemType, itemName, 1);
            SendClientMessage(playerid, COLOR_GREEN, "BAIT: Anda menggunakan umpan cacing untuk memancing!");
            // Here you could add fishing bonus or effect
        }
        else if(!strcmp(itemName, "Corn", true))
        {
            RemovePlayerInventoryItem(playerid, itemType, itemName, 1);
            SendClientMessage(playerid, COLOR_GREEN, "BAIT: Anda menggunakan umpan jagung untuk memancing!");
        }
        else
        {
            SendClientMessage(playerid, COLOR_RED, "ERROR: Umpan tidak dikenal!");
        }
    }
    else if(!strcmp(itemType, "food", true))
    {
        // Handle food items
        RemovePlayerInventoryItem(playerid, itemType, itemName, 1);

        new healthIncrease = 10; // Default health increase
        if(!strcmp(itemName, "Burger", true)) healthIncrease = 15;
        else if(!strcmp(itemName, "Pizza", true)) healthIncrease = 20;

        new Float:currentHealth;
        GetPlayerHealth(playerid, currentHealth);
        SetPlayerHealth(playerid, currentHealth + healthIncrease);

        new msg[128];
        format(msg, sizeof(msg), "FOOD: Anda memakan %s dan mendapat +%d HP!", itemName, healthIncrease);
        SendClientMessage(playerid, COLOR_GREEN, msg);
    }
    else
    {
        SendClientMessage(playerid, COLOR_RED, "ERROR: Tipe item tidak dikenal atau tidak bisa digunakan!");
    }

    return 1;
}

// Command untuk cek stats - removed duplicate, using enhanced version below

// Command untuk change password
CMD:changepass(playerid, params[])
{
    if(!PlayerInfo[playerid][pLogged])
    {
        new error_msg[] = "Anda harus login terlebih dahulu!";
        SendClientMessage(playerid, 0xFF0000FF, error_msg);
        return 1;
    }
    
    if(strlen(params) < 4)
    {
        new usage_msg[] = "Gunakan: /changepass [password_baru] (minimal 4 karakter)";
        SendClientMessage(playerid, 0xFF0000FF, usage_msg);
        return 1;
    }
    
    new hashedPassword[129];
    HashPassword(params, hashedPassword, sizeof(hashedPassword));
    
    new query[256];
    mysql_format(g_SQL, query, sizeof(query), "UPDATE `players` SET `password` = '%s' WHERE `id` = %d", 
        hashedPassword, PlayerInfo[playerid][pID]);
    mysql_tquery(g_SQL, query);
    
    new success_msg[] = "Password berhasil diubah!";
    SendClientMessage(playerid, 0x00FF00FF, success_msg);
    return 1;
}

// Command untuk give money
CMD:givemoney(playerid, params[])
{
    new targetid, amount;
    if(sscanf(params, "ui", targetid, amount))
    {
        new usage_msg[] = "Gunakan: /givemoney [playerid] [amount]";
        SendClientMessage(playerid, 0xFF0000FF, usage_msg);
        return 1;
    }
    
    if(!IsPlayerConnected(targetid))
    {
        new error_msg[] = "Player tidak ditemukan!";
        SendClientMessage(playerid, 0xFF0000FF, error_msg);
        return 1;
    }
    
    if(!PlayerInfo[targetid][pLogged])
    {
        new error_msg[] = "Target player belum login!";
        SendClientMessage(playerid, 0xFF0000FF, error_msg);
        return 1;
    }
    
    GivePlayerMoney(targetid, amount);
    
    new string[128];
    
    format(string, sizeof(string), "Anda memberikan $%d kepada %s", amount, PlayerInfo[targetid][pName]);
    SendClientMessage(playerid, 0x00FF00FF, string);
    
    format(string, sizeof(string), "Anda menerima $%d dari %s", amount, PlayerInfo[playerid][pName]);
    SendClientMessage(targetid, 0x00FF00FF, string);
    
    return 1;
}

public OnPlayerSpawn(playerid)
{
    if(!PlayerInfo[playerid][pLogged])
    {
        new error_msg[] = "Anda harus login terlebih dahulu!";
        SendClientMessage(playerid, 0xFF0000FF, error_msg);
        Kick(playerid);
        return 1;
    }

    // Check if player is dead and apply death mode
    if(PlayerInfo[playerid][pIsDead])
    {
        // Set position to death location FIRST (before any other effects)
        SetPlayerPos(playerid, PlayerInfo[playerid][pDeathX], PlayerInfo[playerid][pDeathY], PlayerInfo[playerid][pDeathZ]);
        SetPlayerFacingAngle(playerid, PlayerInfo[playerid][pDeathAngle]);

        // Apply death animation - crackhead idle lying down
        new animlib[32] = "CRACK";
        new animname[32] = "crckidle2";
        ApplyAnimation(playerid, animlib, animname, 4.0, 1, 0, 0, 1, 0);

        // Apply death animation with delay to ensure it works
        new funcname[] = "ApplyDeathModeImmediate";
        new formatstr[] = "i";
        SetTimerEx(funcname, 100, false, formatstr, playerid);

        // Set drunk level for screen shake
        SetPlayerDrunkLevel(playerid, 999999);

        // Show red screen
        TextDrawShowForPlayer(playerid, RedScreen);

        // Keep original skin
        if(PlayerInfo[playerid][pSkin] > 0)
        {
            SetPlayerSkin(playerid, PlayerInfo[playerid][pSkin]);
        }

        // Set low health
        SetPlayerHealth(playerid, 1.0);

        // Don't send message here - message already sent in other functions to prevent spam

        // Use animation to restrict movement (not system freeze)
        TogglePlayerControllable(playerid, true);

        return 1;
    }

    // Normal spawn for alive players - use saved position
    SetPlayerPos(playerid, PlayerInfo[playerid][pPosX], PlayerInfo[playerid][pPosY], PlayerInfo[playerid][pPosZ]);
    SetPlayerFacingAngle(playerid, PlayerInfo[playerid][pAngle]);

    // Set player skin
    if(PlayerInfo[playerid][pSkin] > 0)
    {
        SetPlayerSkin(playerid, PlayerInfo[playerid][pSkin]);
    }

    // Set player health and armour (ensure health doesn't exceed max health)
    SetPlayerHealthSafe(playerid, PlayerInfo[playerid][pHealth]);
    SetPlayerArmour(playerid, PlayerInfo[playerid][pArmour]);

    // Create time textdraw for player (only if not already created)
    if(PlayerInfo[playerid][pTimeTextDraw][0] == PlayerText:INVALID_TEXT_DRAW &&
       PlayerInfo[playerid][pTimeTextDraw][1] == PlayerText:INVALID_TEXT_DRAW)
    {
        CreateTimeTextDraw(playerid);
    }

    // Create HBE textdraw and progress bars for player (only if not already created)
    if(PlayerTD[playerid][0] == PlayerText:INVALID_TEXT_DRAW)
    {
        CreateHBETextDraw(playerid);
    }

    // Start HBE timer (only if not already running)
    if(PlayerInfo[playerid][pHBETimer] == 0)
    {
        new funcname[] = "UpdateHBESystem";
        new formatstr[] = "i";
        PlayerInfo[playerid][pHBETimer] = SetTimerEx(funcname, 60000, true, formatstr, playerid); // Update every minute
    }



    return 1;
}

// Command untuk melihat stats player dalam dialog
CMD:stats(playerid, params[])
{
    if(!PlayerInfo[playerid][pLogged])
    {
        Dialog_Show(playerid, StatsError, DIALOG_STYLE_MSGBOX, "Error",
            "{999999}ERROR: You don't have privilege to use this command", "OK", "");
        return 1;
    }

    new moneyStr[32], timeStr[32];
    FormatMoney(PlayerInfo[playerid][pMoney], moneyStr, sizeof(moneyStr));

    // Calculate total playing time including current session
    new totalPlayingTime = PlayerInfo[playerid][pPlayingTime];
    if(PlayerInfo[playerid][pLoginTime] > 0)
    {
        new currentSession = gettime() - PlayerInfo[playerid][pLoginTime];
        totalPlayingTime += currentSession;
    }

    FormatPlayingTime(totalPlayingTime, timeStr, sizeof(timeStr));

    // Get current health and armour
    new Float:currentHealth, Float:currentArmour;
    GetPlayerHealth(playerid, currentHealth);
    GetPlayerArmour(playerid, currentArmour);

    new statsMsg[2048];

    // Get gender string
    new genderStr[16];
    if(PlayerInfo[playerid][pGender] == 0)
        format(genderStr, sizeof(genderStr), "{B0C4DE}Male");
    else
        format(genderStr, sizeof(genderStr), "{B0C4DE}Female");

    // Get player rank based on level
    new playerRank[16];
    GetPlayerRank(PlayerInfo[playerid][pLevel], playerRank, sizeof(playerRank));

    // Create the new formatted stats message with colors matching the image
    format(statsMsg, sizeof(statsMsg),
        "{FFFF00}IC Information:\n\
        {FFFFFF}Gender: [%s{FFFFFF}] Origin: [{B0C4DE}%s{FFFFFF}] Money: [{00FF00}%s{FFFFFF}] Phone credit: [{B0C4DE}0 points{FFFFFF}]\n\
        {FFFFFF}Jobs: [{00FFFF}Mechanic, Truck Driver{FFFFFF}]\n\
        {FFFFFF}Married with: [{B0C4DE}None{FFFFFF}] Wealth rating: [{B0C4DE}Medium-Class{FFFFFF}]\n\n\
        {FFFF00}OOC Information:\n\
        {FFFFFF}Player rank: [{B0C4DE}%s{FFFFFF}] Levels: [{B0C4DE}%d{FFFFFF}] Time played: [{00FFFF}%s{FFFFFF}]\n\
        {FFFFFF}Vehicles (5/5): [ {B0C4DE}Road Train, Squalo, Tow Truck, Flatbed, ZR-350 {FFFFFF}]\n\
        {FFFFFF}World: [{B0C4DE}%d{FFFFFF}] Interior: [{B0C4DE}%d{FFFFFF}] MaxHP: [{00FFFF}%.1f{FFFFFF}] Health: [{FF0000}%.1f{FFFFFF}] Armour: [{A9A9A9}%.1f{FFFFFF}]\n\
        {FFFFFF}Gold Points: [{FFFF00}0{FFFFFF}] Warns: [{FF6347}%d{FFFFFF}/{FF0000}10{FFFFFF}]\n\
        {FFFFFF}Property list moved to '/myproperty' or '/mp'",
        genderStr,
        PlayerInfo[playerid][pOrigin],
        moneyStr,
        playerRank,
        PlayerInfo[playerid][pLevel], // Using level as paychecks for now
        timeStr,
        PlayerInfo[playerid][pVirtualWorld],
        PlayerInfo[playerid][pInterior],
        PlayerInfo[playerid][pMaxHealth],
        currentHealth,
        currentArmour,
        PlayerInfo[playerid][pWarnCount]);

    Dialog_Show(playerid, Stats, DIALOG_STYLE_MSGBOX, PlayerInfo[playerid][pName], statsMsg, "Close", "");
    return 1;
}

// Command to check other player's statistics (Admin Level 1+)
CMD:checkstats(playerid, params[])
{
    if(!PlayerInfo[playerid][pLogged])
        return SendClientMessage(playerid, 0xFF0000FF, "ERROR: Anda belum login!");

    if(PlayerInfo[playerid][pAdminLevel] < 1)
        return SendClientMessage(playerid, 0xFF0000FF, "ERROR: Hanya admin level 1+ yang bisa menggunakan command ini!");

    new targetid;
    if(sscanf(params, "u", targetid))
        return SendClientMessage(playerid, 0x808080FF, "USAGE: /checkstats [playerid]");

    if(!IsPlayerConnected(targetid) || !PlayerInfo[targetid][pLogged])
        return SendClientMessage(playerid, 0xFF0000FF, "ERROR: Player tidak online atau belum login!");

    new moneyStr[32], timeStr[32];
    FormatMoney(PlayerInfo[targetid][pMoney], moneyStr, sizeof(moneyStr));

    // Calculate total playing time including current session
    new totalPlayingTime = PlayerInfo[targetid][pPlayingTime];
    if(PlayerInfo[targetid][pLoginTime] > 0)
    {
        new currentSession = gettime() - PlayerInfo[targetid][pLoginTime];
        totalPlayingTime += currentSession;
    }

    FormatPlayingTime(totalPlayingTime, timeStr, sizeof(timeStr));

    // Get current health and armour
    new Float:currentHealth, Float:currentArmour;
    GetPlayerHealth(targetid, currentHealth);
    GetPlayerArmour(targetid, currentArmour);

    new statsMsg[2048];

    // Get gender string for target
    new genderStr[16];
    if(PlayerInfo[targetid][pGender] == 0)
        format(genderStr, sizeof(genderStr), "{B0C4DE}Male");
    else
        format(genderStr, sizeof(genderStr), "{B0C4DE}Female");

    // Get player rank based on level for target
    new playerRank[16];
    GetPlayerRank(PlayerInfo[targetid][pLevel], playerRank, sizeof(playerRank));

    // Create the formatted stats message for target player - SAME FORMAT AS /stats
    format(statsMsg, sizeof(statsMsg),
        "{FFFF00}IC Information:\n\
        {FFFFFF}Gender: [%s{FFFFFF}] Origin: [{B0C4DE}%s{FFFFFF}] Money: [{00FF00}%s{FFFFFF}] Phone credit: [{B0C4DE}0 points{FFFFFF}]\n\
        {FFFFFF}Jobs: [{00FFFF}Mechanic, Truck Driver{FFFFFF}]\n\
        {FFFFFF}Married with: [{B0C4DE}None{FFFFFF}] Wealth rating: [{B0C4DE}Medium-Class{FFFFFF}]\n\n\
        {FFFF00}OOC Information:\n\
        {FFFFFF}Player rank: [{B0C4DE}%s{FFFFFF}] Levels: [{B0C4DE}%d{FFFFFF}] Time played: [{00FFFF}%s{FFFFFF}]\n\
        {FFFFFF}Vehicles (5/5): [ {B0C4DE}Road Train, Squalo, Tow Truck, Flatbed, ZR-350 {FFFFFF}]\n\
        {FFFFFF}World: [{B0C4DE}%d{FFFFFF}] Interior: [{B0C4DE}%d{FFFFFF}] MaxHP: [{00FFFF}%.1f{FFFFFF}] Health: [{FF0000}%.1f{FFFFFF}] Armour: [{A9A9A9}%.1f{FFFFFF}]\n\
        {FFFFFF}Gold Points: [{FFFF00}0{FFFFFF}] Warns: [{FF6347}%d{FFFFFF}/{FF0000}10{FFFFFF}]\n\
        {FFFFFF}Property list moved to '/myproperty' or '/mp'",
        genderStr,
        PlayerInfo[targetid][pOrigin],
        moneyStr,
        playerRank,
        PlayerInfo[targetid][pLevel], // Using level as paychecks for now
        timeStr,
        PlayerInfo[targetid][pVirtualWorld],
        PlayerInfo[targetid][pInterior],
        PlayerInfo[targetid][pMaxHealth],
        currentHealth,
        currentArmour,
        PlayerInfo[targetid][pWarnCount]);

    Dialog_Show(playerid, Stats, DIALOG_STYLE_MSGBOX, PlayerInfo[targetid][pName], statsMsg, "Close", "");
    return 1;
}

// Command: Goto player (Admin Level 1+)
CMD:goto(playerid, params[])
{
    if(!PlayerInfo[playerid][pLogged])
        return SendClientMessage(playerid, 0xFF0000FF, "ERROR: Anda belum login!");

    if(PlayerInfo[playerid][pAdminLevel] < 1)
        return SendClientMessage(playerid, 0xFF0000FF, "ERROR: Hanya admin level 1+ yang bisa menggunakan command ini!");

    new targetid;
    if(sscanf(params, "u", targetid))
        return SendClientMessage(playerid, 0x808080FF, "USAGE: /goto [playerid]");

    if(!IsPlayerConnected(targetid) || !PlayerInfo[targetid][pLogged])
        return SendClientMessage(playerid, 0xFF0000FF, "ERROR: Player tidak online atau belum login!");

    if(playerid == targetid)
        return SendClientMessage(playerid, 0xFF0000FF, "ERROR: Anda tidak bisa goto ke diri sendiri!");

    // Get target player position
    new Float:targetX, Float:targetY, Float:targetZ;
    GetPlayerPos(targetid, targetX, targetY, targetZ);

    // Get target player interior and virtual world
    new targetInterior = GetPlayerInterior(targetid);
    new targetVirtualWorld = GetPlayerVirtualWorld(targetid);

    // Teleport admin to position above target player (add 2.0 to Z coordinate)
    SetPlayerPos(playerid, targetX, targetY, targetZ + 2.0);
    SetPlayerInterior(playerid, targetInterior);
    SetPlayerVirtualWorld(playerid, targetVirtualWorld);

    // Update admin's position, interior and virtual world data
    PlayerInfo[playerid][pPosX] = targetX;
    PlayerInfo[playerid][pPosY] = targetY;
    PlayerInfo[playerid][pPosZ] = targetZ + 2.0;
    PlayerInfo[playerid][pInterior] = targetInterior;
    PlayerInfo[playerid][pVirtualWorld] = targetVirtualWorld;

    // Get names for logging
    new targetName[MAX_PLAYER_NAME], adminName[32];
    GetPlayerName(targetid, targetName, sizeof(targetName));
    GetPlayerAdminName(playerid, adminName, sizeof(adminName));

    // Send messages
    new message[128];
    format(message, sizeof(message), "TELE: {FFFFFF}You have teleported to {FFFF00}%s", targetName);
    SendClientMessage(playerid, COLOR_KAKZAH, message);

    format(message, sizeof(message), "AdmCmd: Admin %s has teleported to you", adminName);
    SendClientMessage(targetid, COLOR_ADMIN, message);

    // Log admin command
    new logStr[256];
    format(logStr, sizeof(logStr), "ADMIN: %s used /goto to teleport to %s", adminName, targetName);
    print(logStr);

    return 1;
}

// Command: Forward teleport (Admin Level 1+)
CMD:forward(playerid, params[])
{
    if(!PlayerInfo[playerid][pLogged])
        return SendClientMessage(playerid, 0xFF0000FF, "ERROR: Anda belum login!");

    if(PlayerInfo[playerid][pAdminLevel] < 1)
        return SendClientMessage(playerid, 0xFF0000FF, "ERROR: Hanya admin level 1+ yang bisa menggunakan command ini!");

    // Get current player position and angle
    new Float:currentX, Float:currentY, Float:currentZ, Float:currentAngle;
    GetPlayerPos(playerid, currentX, currentY, currentZ);
    GetPlayerFacingAngle(playerid, currentAngle);

    // Calculate new position (2.0 units forward)
    new Float:newX, Float:newY;
    newX = currentX + (2.0 * floatsin(-currentAngle, degrees));
    newY = currentY + (2.0 * floatcos(-currentAngle, degrees));

    // Teleport player to new position
    SetPlayerPos(playerid, newX, newY, currentZ);

    // Get admin name for logging
    new adminName[32];
    GetPlayerAdminName(playerid, adminName, sizeof(adminName));

    // Send message
    SendClientMessage(playerid, COLOR_KAKZAH, "TELE: {FFFFFF}You have moved forward");

    // Log admin command
    new logStr[256];
    format(logStr, sizeof(logStr), "ADMIN: %s used /forward", adminName);
    print(logStr);

    return 1;
}

// Command: Teleport to the top (Admin Level 1+)
CMD:tothetop(playerid, params[])
{
    if(!PlayerInfo[playerid][pLogged])
        return SendClientMessage(playerid, 0xFF0000FF, "ERROR: Anda belum login!");

    if(PlayerInfo[playerid][pAdminLevel] < 1)
        return SendClientMessage(playerid, 0xFF0000FF, "ERROR: Hanya admin level 1+ yang bisa menggunakan command ini!");

    // Get current player position
    new Float:currentX, Float:currentY, Float:currentZ;
    GetPlayerPos(playerid, currentX, currentY, currentZ);

    // Teleport player 10.0 units up
    SetPlayerPos(playerid, currentX, currentY, currentZ + 10.0);

    // Get admin name for logging
    new adminName[32];
    GetPlayerAdminName(playerid, adminName, sizeof(adminName));

    // Send message
    SendClientMessage(playerid, COLOR_KAKZAH, "TELE: {FFFFFF}You have moved to the top");

    // Log admin command
    new logStr[256];
    format(logStr, sizeof(logStr), "ADMIN: %s used /tothetop", adminName);
    print(logStr);

    return 1;
}

// Command: Goto coordinates (Admin Level 8+)
CMD:gotoco(playerid, params[])
{
    if(!PlayerInfo[playerid][pLogged])
        return SendClientMessage(playerid, 0xFF0000FF, "ERROR: Anda belum login!");

    if(PlayerInfo[playerid][pAdminLevel] < 8)
        return SendClientMessage(playerid, 0xFF0000FF, "ERROR: Hanya admin level 8+ yang bisa menggunakan command ini!");

    new Float:coordX, Float:coordY, Float:coordZ, interior;

    if(sscanf(params, "fffi", coordX, coordY, coordZ, interior))
    {
        SendClientMessage(playerid, 0x808080FF, "USAGE: /gotoco [x] [y] [z] [interior]");
        SendClientMessage(playerid, 0xFFFFFFFF, "INFO: Teleport ke koordinat tertentu dengan interior");
        SendClientMessage(playerid, 0xFFFFFFFF, "INFO: Contoh: /gotoco 1479.23 -1643.12 14.04 0");
        return 1;
    }

    // Validate coordinates (basic range check for San Andreas)
    if(coordX < -3000.0 || coordX > 3000.0 || coordY < -3000.0 || coordY > 3000.0)
    {
        return SendClientMessage(playerid, 0xFF0000FF, "ERROR: Koordinat tidak valid! Range: -3000 to 3000");
    }

    if(coordZ < -100.0 || coordZ > 1000.0)
    {
        return SendClientMessage(playerid, 0xFF0000FF, "ERROR: Koordinat Z tidak valid! Range: -100 to 1000");
    }

    if(interior < 0 || interior > 255)
    {
        return SendClientMessage(playerid, 0xFF0000FF, "ERROR: Interior tidak valid! Range: 0 to 255");
    }

    // Get current position for distance calculation
    new Float:currentX, Float:currentY, Float:currentZ;
    GetPlayerPos(playerid, currentX, currentY, currentZ);
    new Float:distance = GetDistanceBetweenPoints(currentX, currentY, currentZ, coordX, coordY, coordZ);

    // Teleport to coordinates
    SetPlayerPos(playerid, coordX, coordY, coordZ);
    SetPlayerInterior(playerid, interior);
    SetPlayerVirtualWorld(playerid, 0); // Default virtual world

    // Update player's saved interior
    PlayerInfo[playerid][pInterior] = interior;

    // Get admin name for logging
    new adminName[32];
    GetPlayerAdminName(playerid, adminName, sizeof(adminName));

    // Send success message
    new message[256];
    format(message, sizeof(message), "TELE: {FFFFFF}You have teleported to {FFFF00}(%.2f, %.2f, %.2f) {FFFFFF}Interior: {00FF00}%d {FFFFFF}Distance: {00FF00}%.2fm",
           coordX, coordY, coordZ, interior, distance);
    SendClientMessage(playerid, COLOR_KAKZAH, message);

    // Log admin command
    new logStr[256];
    format(logStr, sizeof(logStr), "ADMIN: %s used /gotoco to coordinates (%.2f, %.2f, %.2f) Interior: %d",
           adminName, coordX, coordY, coordZ, interior);
    print(logStr);

    return 1;
}

// Helper functions removed - using easyDialog system

// Add helper function for camera movement
stock InterpolateCamera(playerid, Float:x1, Float:y1, Float:z1, Float:x2, Float:y2, Float:z2, time)
{
    InterpolateCameraPos(playerid, x1, y1, z1, x2, y2, z2, time);
    InterpolateCameraLookAt(playerid, x1, y1, z1, x2, y2, z2, time);
    return 1;
}

// ==================== ROLEPLAY COMMANDS ====================

// /me command
CMD:me(playerid, params[])
{
    if(!PlayerInfo[playerid][pLogged]) return SendClientMessage(playerid, 0x999999FF, "ERROR: You don't have privilege to use this command");

    // Disable RP commands when admin is on duty or jailed
    if(PlayerInfo[playerid][pAdminDuty] && PlayerInfo[playerid][pAdminLevel] > 0)
        return SendClientMessage(playerid, 0x999999FF, "ERROR: You don't have privilege to use this command");

    if(PlayerInfo[playerid][pJailed])
        return SendClientMessage(playerid, 0xFF0000FF, "ERROR: Anda tidak bisa menggunakan command IC saat di jail! Gunakan /b untuk OOC chat.");

    if(isnull(params)) return SendClientMessage(playerid, 0x808080FF, "USAGE: /me [action]");

    new string[256], name[MAX_PLAYER_NAME];
    GetPlayerDisplayName(playerid, name, sizeof(name));

    format(string, sizeof(string), "* %s %s", name, params);

    // Send to nearby players
    new Float:x, Float:y, Float:z;
    GetPlayerPos(playerid, x, y, z);

    for(new i = 0; i < MAX_PLAYERS; i++)
    {
        if(IsPlayerConnected(i) && PlayerInfo[i][pLogged])
        {
            if(GetPlayerDistanceFromPoint(i, x, y, z) <= 30.0)
            {
                SendClientMessage(i, 0xC2A2DAFF, string);
            }
        }
    }

    return 1;
}

// /do command
CMD:do(playerid, params[])
{
    if(!PlayerInfo[playerid][pLogged]) return SendClientMessage(playerid, 0x999999FF, "ERROR: You don't have privilege to use this command");

    // Disable RP commands when admin is on duty or jailed
    if(PlayerInfo[playerid][pAdminDuty] && PlayerInfo[playerid][pAdminLevel] > 0)
        return SendClientMessage(playerid, 0x999999FF, "ERROR: You don't have privilege to use this command");

    if(PlayerInfo[playerid][pJailed])
        return SendClientMessage(playerid, 0xFF0000FF, "ERROR: Anda tidak bisa menggunakan command IC saat di jail! Gunakan /b untuk OOC chat.");

    if(isnull(params)) return SendClientMessage(playerid, 0x808080FF, "USAGE: /do [description]");

    new string[256], name[MAX_PLAYER_NAME];
    GetPlayerDisplayName(playerid, name, sizeof(name));

    format(string, sizeof(string), "* %s (( %s ))", params, name);

    // Send to nearby players
    new Float:x, Float:y, Float:z;
    GetPlayerPos(playerid, x, y, z);

    for(new i = 0; i < MAX_PLAYERS; i++)
    {
        if(IsPlayerConnected(i) && PlayerInfo[i][pLogged])
        {
            if(GetPlayerDistanceFromPoint(i, x, y, z) <= 30.0)
            {
                SendClientMessage(i, 0xC2A2DAFF, string);
            }
        }
    }

    return 1;
}

// /help command
CMD:help(playerid, params[])
{
    if(!PlayerInfo[playerid][pLogged]) return SendClientMessage(playerid, 0x999999FF, "ERROR: You don't have privilege to use this command");

    Dialog_Show(playerid, Help, DIALOG_STYLE_LIST, "Menu Bantuan - Pilih Kategori",
        "General Commands\nRoleplay Commands", "Pilih", "Tutup");

    return 1;
}

// /levels command
CMD:levels(playerid, params[])
{
    if(!PlayerInfo[playerid][pLogged]) return SendClientMessage(playerid, 0x999999FF, "ERROR: You don't have privilege to use this command");

    new currentHours = PlayerInfo[playerid][pPlayingTime] / 3600;
    new currentLevel = PlayerInfo[playerid][pLevel];

    // Calculate hours needed for next level
    new hoursNeeded = 0;
    new nextLevelRequirement = 0;

    switch(currentLevel)
    {
        case 1: nextLevelRequirement = 5;   // Need 5 hours total for level 2
        case 2: nextLevelRequirement = 10;  // Need 10 hours total for level 3
        case 3: nextLevelRequirement = 25;  // Need 25 hours total for level 4
        case 4: nextLevelRequirement = 50;  // Need 50 hours total for level 5
        default: nextLevelRequirement = 0;  // Max level reached
    }

    if(nextLevelRequirement > 0)
    {
        hoursNeeded = nextLevelRequirement - currentHours;
        if(hoursNeeded < 0) hoursNeeded = 0;
    }

    if(hoursNeeded < 0) hoursNeeded = 0;

    new levelInfo[1024];
    new dialogTitle[128];

    // Create level info content (same for both cases)
    format(levelInfo, sizeof(levelInfo),
        "Level\tRank\tHours\n\
        {FFFF00}1\t{FFFFFF}Newbie\t{FFFFFF}0 Hours\n\
        {FFFF00}2\t{FFFFFF}Trainer\t{FFFFFF}5 Hours\n\
        {FFFF00}3\t{FFFFFF}Starter\t{FFFFFF}10 Hours\n\
        {FFFF00}4\t{FFFFFF}Playing Fun\t{FFFFFF}25 Hours\n\
        {FFFF00}5\t{FFFFFF}Hard Work\t{FFFFFF}50 Hours");

    // Create dialog title based on level status
    if(currentLevel >= 5)
    {
        format(dialogTitle, sizeof(dialogTitle), "Level Requirements - Max Level Reached!");
    }
    else
    {
        format(dialogTitle, sizeof(dialogTitle), "Level Requirements - Next Level: %d Hours need", hoursNeeded);
    }

    Dialog_Show(playerid, Levels, DIALOG_STYLE_TABLIST_HEADERS, dialogTitle, levelInfo, "OK", "");

    return 1;
}

// Function to show origin selection with pagination
stock ShowOriginDialog(playerid, page)
{
    new originList[2048], tempStr[64];
    new totalCountries = sizeof(gOrigins);
    new itemsPerPage = 10;
    new startIndex = page * itemsPerPage;
    new endIndex = startIndex + itemsPerPage;
    new itemCount = 0;

    // Add countries for current page
    for(new i = startIndex; i < endIndex && i < totalCountries; i++)
    {
        format(tempStr, sizeof(tempStr), "%s\n", gOrigins[i]);
        strcat(originList, tempStr);
        itemCount++;
    }

    // Add navigation options
    new hasBack = (page > 0);
    new hasNext = (endIndex < totalCountries);

    if(hasBack || hasNext)
    {
        strcat(originList, "{808080}--- Navigation ---\n");

        if(hasBack)
        {
            strcat(originList, "{FFFF00}< Back\n");
            itemCount++;
        }

        if(hasNext)
        {
            strcat(originList, "{FFFF00}Next >\n");
            itemCount++;
        }
    }

    // Remove last newline
    new len = strlen(originList);
    if(len > 0 && originList[len-1] == '\n')
        originList[len-1] = '\0';

    new title[64];
    format(title, sizeof(title), "Pilih Asal Negara (Page %d/%d)", page + 1, (totalCountries + itemsPerPage - 1) / itemsPerPage);

    Dialog_Show(playerid, Origin, DIALOG_STYLE_LIST, title, originList, "Pilih", "Kembali");
    return 1;
}

// Function to get player rank based on level (sesuai dengan /levels)
stock GetPlayerRank(level, output[], maxlength)
{
    switch(level)
    {
        case 1: format(output, maxlength, "Newbie");
        case 2: format(output, maxlength, "Trainer");
        case 3: format(output, maxlength, "Starter");
        case 4: format(output, maxlength, "Playing Fun");
        case 5: format(output, maxlength, "Hard Work");
        default: format(output, maxlength, "Newbie");
    }
    return 1;
}

// Function to calculate distance between two 3D points
stock Float:GetDistanceBetweenPoints(Float:x1, Float:y1, Float:z1, Float:x2, Float:y2, Float:z2)
{
    return floatsqroot(floatpower(x2 - x1, 2.0) + floatpower(y2 - y1, 2.0) + floatpower(z2 - z1, 2.0));
}

// Function to update player interior and virtual world
stock UpdatePlayerInteriorVW(playerid)
{
    if(!PlayerInfo[playerid][pLogged]) return 0;

    new currentInterior = GetPlayerInterior(playerid);
    new currentVW = GetPlayerVirtualWorld(playerid);

    // Update if changed
    if(PlayerInfo[playerid][pInterior] != currentInterior)
    {
        PlayerInfo[playerid][pInterior] = currentInterior;
    }

    if(PlayerInfo[playerid][pVirtualWorld] != currentVW)
    {
        PlayerInfo[playerid][pVirtualWorld] = currentVW;
    }

    return 1;
}

// Function to get default admin name based on level
stock GetDefaultAdminName(level, output[], maxlength)
{
    switch(level)
    {
        case 1: format(output, maxlength, "Helper");
        case 2: format(output, maxlength, "Senior Helper");
        case 3: format(output, maxlength, "Admin Level 1");
        case 4: format(output, maxlength, "Admin Level 2");
        case 5: format(output, maxlength, "Admin Level 3");
        case 6: format(output, maxlength, "Admin Level 4");
        case 7: format(output, maxlength, "Senior Admin");
        case 8: format(output, maxlength, "Management Admin");
        case 9: format(output, maxlength, "Vice Head Admin");
        case 10: format(output, maxlength, "Head Admin");
        default: format(output, maxlength, "Unknown");
    }
    return 1;
}

// Function to get display name (admin name if on duty, regular name if not)
stock GetPlayerDisplayName(playerid, output[], maxlength)
{
    // Only use admin name if: 1) Admin level > 0, 2) Currently ON duty, 3) Has custom admin name
    if(PlayerInfo[playerid][pAdminLevel] > 0 && PlayerInfo[playerid][pAdminDuty] == 1 && strlen(PlayerInfo[playerid][pAdminName]) > 0)
    {
        format(output, maxlength, "%s", PlayerInfo[playerid][pAdminName]);
    }
    else
    {
        // Use regular player name (with underscore replaced by space)
        GetPlayerName(playerid, output, maxlength);
        for(new i = 0; i < strlen(output); i++)
        {
            if(output[i] == '_') output[i] = ' ';
        }
    }
    return 1;
}

// Function to add warn to database
stock AddPlayerWarn(const playerName[], const warnType[], const adminName[], const reason[])
{
    new query[512], currentDate[32];

    // Get current date
    new year, month, day;
    getdate(year, month, day);
    format(currentDate, sizeof(currentDate), "%02d/%02d/%d", day, month, year);

    // Insert warn into database
    mysql_format(g_SQL, query, sizeof(query),
        "INSERT INTO `player_warns` (`player_name`, `warn_type`, `admin_name`, `warn_date`, `reason`) VALUES ('%s', '%s', '%s', '%s', '%s')",
        playerName, warnType, adminName, currentDate, reason);

    mysql_tquery(g_SQL, query);

    return 1;
}

// Function to get admin name (always use admin name for admin-related actions)
stock GetPlayerAdminName(playerid, output[], maxlength)
{
    if(PlayerInfo[playerid][pAdminLevel] > 0)
    {
        if(strlen(PlayerInfo[playerid][pAdminName]) > 0)
        {
            format(output, maxlength, "%s", PlayerInfo[playerid][pAdminName]);
        }
        else
        {
            GetDefaultAdminName(PlayerInfo[playerid][pAdminLevel], output, maxlength);
        }
    }
    else
    {
        // Not an admin, use regular name
        GetPlayerName(playerid, output, maxlength);
        for(new i = 0; i < strlen(output); i++)
        {
            if(output[i] == '_') output[i] = ' ';
        }
    }
    return 1;
}

// Function to update player name in tab list
stock UpdatePlayerTabName(playerid)
{
    if(PlayerInfo[playerid][pAdminDuty] && PlayerInfo[playerid][pAdminLevel] > 0)
    {
        // Set red transparent color (no map icon)
        SetPlayerColor(playerid, 0xFF000000);

        // Get admin name for TAB
        new adminName[32], originalName[MAX_PLAYER_NAME];
        GetPlayerName(playerid, originalName, sizeof(originalName));

        if(strlen(PlayerInfo[playerid][pAdminName]) > 0)
        {
            format(adminName, sizeof(adminName), "%s", PlayerInfo[playerid][pAdminName]);
        }
        else
        {
            GetDefaultAdminName(PlayerInfo[playerid][pAdminLevel], adminName, sizeof(adminName));
        }

        // Replace spaces with underscores for SetPlayerName (SA-MP requirement)
        for(new i = 0; i < strlen(adminName); i++)
        {
            if(adminName[i] == ' ') adminName[i] = '_';
        }

        // Change name in TAB list
        SetPlayerName(playerid, adminName);
    }
    else
    {
        // Set white transparent color and restore original name (no map icon)
        SetPlayerColor(playerid, 0xFFFFFF00);

        // Restore original name from PlayerInfo
        SetPlayerName(playerid, PlayerInfo[playerid][pName]);
    }
    return 1;
}

// Admin Commands

// /jetpack command (Admin Level 1+)
CMD:jetpack(playerid, params[])
{
    if(!PlayerInfo[playerid][pLogged]) return SendClientMessage(playerid, 0x999999FF, "ERROR: You must be logged in first!");

    if(PlayerInfo[playerid][pAdminLevel] < 1)
        return SendClientMessage(playerid, 0x999999FF, "ERROR: You are not an admin!");

    if(GetPlayerSpecialAction(playerid) == SPECIAL_ACTION_USEJETPACK)
    {
        SetPlayerSpecialAction(playerid, SPECIAL_ACTION_NONE);
        SendClientMessage(playerid, COLOR_KAKZAH, "JETPACK: {FF0000}Deactive.");
    }
    else
    {
        SetPlayerSpecialAction(playerid, SPECIAL_ACTION_USEJETPACK);
        SendClientMessage(playerid, COLOR_KAKZAH, "JETPACK: {00FF00}Active.");
    }

    return 1;
}

// /a command - Admin chat (Admin Level 1+)
CMD:a(playerid, params[])
{
    if(!PlayerInfo[playerid][pLogged]) return SendClientMessage(playerid, 0x999999FF, "ERROR: You must be logged in first!");

    if(PlayerInfo[playerid][pAdminLevel] < 1)
        return SendClientMessage(playerid, 0x999999FF, "ERROR: You are not an admin!");

    if(isnull(params)) return SendClientMessage(playerid, 0x808080FF, "USAGE: /a [message]");

    new adminName[32], message[256];

    // Use admin name if on duty, otherwise use regular name
    if(PlayerInfo[playerid][pAdminDuty] && strlen(PlayerInfo[playerid][pAdminName]) > 0)
    {
        format(adminName, sizeof(adminName), "%s", PlayerInfo[playerid][pAdminName]);
    }
    else
    {
        GetPlayerName(playerid, adminName, sizeof(adminName));
        // Replace underscore with space
        for(new i = 0; i < strlen(adminName); i++)
        {
            if(adminName[i] == '_') adminName[i] = ' ';
        }
    }

    format(message, sizeof(message),
        "{FF0000}[ADMIN] {FFFF00}%s (Level %d): {FFFFFF}%s",
        adminName, PlayerInfo[playerid][pAdminLevel], params);

    // Send to all admins
    for(new i = 0; i < MAX_PLAYERS; i++)
    {
        if(IsPlayerConnected(i) && PlayerInfo[i][pLogged] && PlayerInfo[i][pAdminLevel] >= 1)
        {
            SendClientMessage(i, 0xFF0000FF, message);
        }
    }

    return 1;
}

// /aduty or /aod command - Admin duty toggle (Admin Level 1+)
CMD:aduty(playerid, params[])
{
    if(!PlayerInfo[playerid][pLogged]) return SendClientMessage(playerid, 0x999999FF, "ERROR: You must be logged in first!");

    if(PlayerInfo[playerid][pAdminLevel] < 1)
        return SendClientMessage(playerid, 0x999999FF, "ERROR: You are not an admin!");

    if(PlayerInfo[playerid][pAdminDuty])
    {
        // Calculate duty time and add to total
        new dutyTime = gettime() - PlayerInfo[playerid][pDutyStartTime];
        PlayerInfo[playerid][pTotalDutyTime] += dutyTime;
        PlayerInfo[playerid][pDutyStartTime] = 0;

        PlayerInfo[playerid][pAdminDuty] = 0;
        UpdatePlayerTabName(playerid); // Update tab color and name

        // Log admin command
        new logInfo[64];
        format(logInfo, sizeof(logInfo), "Went OFF duty (session: %d seconds)", dutyTime);
        LogAdminCommand(playerid, "/aduty", "", logInfo);

        // Show duty time message
        new hours = dutyTime / 3600;
        new minutes = (dutyTime % 3600) / 60;
        new seconds = dutyTime % 60;

        new dutyMsg[128];
        format(dutyMsg, sizeof(dutyMsg),
            "Admin duty: OFF - Session time: %02d:%02d:%02d", hours, minutes, seconds);
        SendClientMessage(playerid, 0x00FF00FF, dutyMsg);

        // Announce to other admins
        new adminName[32], message[128];
        GetPlayerName(playerid, adminName, sizeof(adminName));
        for(new i = 0; i < strlen(adminName); i++)
        {
            if(adminName[i] == '_') adminName[i] = ' ';
        }

        format(message, sizeof(message),
            "{FFFF00}Admin %s telah OFF duty.", adminName);

        for(new i = 0; i < MAX_PLAYERS; i++)
        {
            if(IsPlayerConnected(i) && PlayerInfo[i][pLogged] && PlayerInfo[i][pAdminLevel] >= 1 && i != playerid)
            {
                SendClientMessage(i, 0xFFFF00FF, message);
            }
        }
    }
    else
    {
        PlayerInfo[playerid][pAdminDuty] = 1;
        PlayerInfo[playerid][pDutyStartTime] = gettime(); // Record duty start time
        UpdatePlayerTabName(playerid); // Update tab color and name
        SendClientMessage(playerid, 0x00FF00FF, "Admin duty: ON - Roleplay commands disabled, gunakan /b untuk OOC");

        // Log admin command
        LogAdminCommand(playerid, "/aduty", "", "Went ON duty");

        // Announce to other admins
        new adminName[32], message[128];
        if(strlen(PlayerInfo[playerid][pAdminName]) > 0)
        {
            format(adminName, sizeof(adminName), "%s", PlayerInfo[playerid][pAdminName]);
        }
        else
        {
            GetPlayerName(playerid, adminName, sizeof(adminName));
            for(new i = 0; i < strlen(adminName); i++)
            {
                if(adminName[i] == '_') adminName[i] = ' ';
            }
        }

        format(message, sizeof(message),
            "{FFFF00}Admin %s telah ON duty.", adminName);

        for(new i = 0; i < MAX_PLAYERS; i++)
        {
            if(IsPlayerConnected(i) && PlayerInfo[i][pLogged] && PlayerInfo[i][pAdminLevel] >= 1 && i != playerid)
            {
                SendClientMessage(i, 0xFFFF00FF, message);
            }
        }
    }

    return 1;
}

// Alias for /aduty
CMD:aod(playerid, params[])
{
    return cmd_aduty(playerid, params);
}

// /setadminname command (Admin Level 10 only)
CMD:setadminname(playerid, params[])
{
    if(!PlayerInfo[playerid][pLogged]) return SendClientMessage(playerid, 0x999999FF, "ERROR: You must be logged in first!");

    if(PlayerInfo[playerid][pAdminLevel] < 10)
        return SendClientMessage(playerid, 0x999999FF, "ERROR: Only Head Admin can use this command!");

    new targetid, adminname[32];
    if(sscanf(params, "us[32]", targetid, adminname))
        return SendClientMessage(playerid, 0x808080FF, "USAGE: /setadminname [playerid] [nama] atau 'default' untuk nama default");

    if(!IsPlayerConnected(targetid) || !PlayerInfo[targetid][pLogged])
        return SendClientMessage(playerid, 0x999999FF, "ERROR: Player is not online or not logged in!");

    if(PlayerInfo[targetid][pAdminLevel] < 1)
        return SendClientMessage(playerid, 0x999999FF, "ERROR: Target is not an admin!");

    new targetName[MAX_PLAYER_NAME], message[128];
    GetPlayerName(targetid, targetName, sizeof(targetName));

    // Check if setting to default name
    if(!strcmp(adminname, "default", true))
    {
        GetDefaultAdminName(PlayerInfo[targetid][pAdminLevel], PlayerInfo[targetid][pAdminName], 32);

        format(message, sizeof(message),
            "Admin name %s berhasil direset ke default: {FFFF00}%s",
            targetName, PlayerInfo[targetid][pAdminName]);
        SendClientMessage(playerid, 0x00FF00FF, message);

        format(message, sizeof(message),
            "Admin name Anda direset ke default: {FFFF00}%s {FFFFFF}oleh {FFFF00}Head Admin",
            PlayerInfo[targetid][pAdminName]);
        SendClientMessage(targetid, 0x00FF00FF, message);
    }
    else
    {
        if(strlen(adminname) > 31)
            return SendClientMessage(playerid, 0x999999FF, "ERROR: Admin name maximum 31 characters!");

        format(PlayerInfo[targetid][pAdminName], 32, "%s", adminname);

        format(message, sizeof(message),
            "Admin name %s berhasil diset menjadi: {FFFF00}%s",
            targetName, adminname);
        SendClientMessage(playerid, 0x00FF00FF, message);

        format(message, sizeof(message),
            "Admin name Anda diset menjadi: {FFFF00}%s {FFFFFF}oleh {FFFF00}Head Admin",
            adminname);
        SendClientMessage(targetid, 0x00FF00FF, message);
    }

    // Save to database immediately
    SavePlayerData(targetid);

    return 1;
}

// /ahelp command - Admin help menu
CMD:ahelp(playerid, params[])
{
    if(!PlayerInfo[playerid][pLogged])
        return SendClientMessage(playerid, 0x999999FF, "ERROR: You must be logged in first!");

    if(PlayerInfo[playerid][pAdminLevel] < 1)
        return SendClientMessage(playerid, 0x999999FF, "ERROR: You are not an admin!");

    new adminLevel = PlayerInfo[playerid][pAdminLevel];
    new levelList[512];

    // Create tablist with admin levels (only show levels player has access to)
    strcat(levelList, "Level\tDescription\n");

    if(adminLevel >= 1)
        strcat(levelList, "Level 1\tBasic Admin Commands\n");
    if(adminLevel >= 2)
        strcat(levelList, "Level 2\tPlayer Management\n");
    if(adminLevel >= 5)
        strcat(levelList, "Level 5\tAdvanced Commands\n");
    if(adminLevel >= 8)
        strcat(levelList, "Level 8\tSenior Admin Commands\n");
    if(adminLevel >= 10)
        strcat(levelList, "Level 10\tHead Admin Commands");

    Dialog_Show(playerid, AHelp, DIALOG_STYLE_TABLIST_HEADERS, "Admin Help - Select Level",
        levelList, "Select", "Close");

    return 1;
}

// /adutytime command - Show admin duty time statistics
CMD:adutytime(playerid, params[])
{
    if(!PlayerInfo[playerid][pLogged]) return SendClientMessage(playerid, 0x999999FF, "ERROR: You don't have privilege to use this command");

    if(PlayerInfo[playerid][pAdminLevel] < 1)
        return SendClientMessage(playerid, 0x999999FF, "ERROR: You don't have privilege to use this command");

    new dutyList[2048], tempStr[128];
    strcat(dutyList, "Player Name\tAdmin Name\tLevel\tDuty Time\n");

    new count = 0;
    for(new i = 0; i < MAX_PLAYERS; i++)
    {
        if(IsPlayerConnected(i) && PlayerInfo[i][pLogged] && PlayerInfo[i][pAdminLevel] > 0)
        {
            new playerName[MAX_PLAYER_NAME], adminName[32], defaultName[32];
            GetPlayerName(i, playerName, sizeof(playerName));

            // Get admin name
            if(strlen(PlayerInfo[i][pAdminName]) > 0)
            {
                format(adminName, sizeof(adminName), "%s", PlayerInfo[i][pAdminName]);
            }
            else
            {
                GetDefaultAdminName(PlayerInfo[i][pAdminLevel], defaultName, sizeof(defaultName));
                format(adminName, sizeof(adminName), "%s", defaultName);
            }

            // Calculate total duty time (including current session if on duty)
            new totalTime = PlayerInfo[i][pTotalDutyTime];
            if(PlayerInfo[i][pAdminDuty])
            {
                totalTime += (gettime() - PlayerInfo[i][pDutyStartTime]);
            }

            // Format time as HH:MM:SS
            new hours = totalTime / 3600;
            new minutes = (totalTime % 3600) / 60;
            new seconds = totalTime % 60;

            // Replace underscore with space in player name
            for(new j = 0; j < strlen(playerName); j++)
            {
                if(playerName[j] == '_') playerName[j] = ' ';
            }

            format(tempStr, sizeof(tempStr), "%s\t%s\t%d\t%02d:%02d:%02d\n",
                playerName, adminName, PlayerInfo[i][pAdminLevel], hours, minutes, seconds);
            strcat(dutyList, tempStr);
            count++;
        }
    }

    if(count == 0)
    {
        strcat(dutyList, "No admin data available\t-\t-\t-\n");
    }

    Dialog_Show(playerid, ADutyTime, DIALOG_STYLE_TABLIST_HEADERS, "Admin Duty Time Statistics",
        dutyList, "OK", "");

    return 1;
}

// Function to show admin commands for specific level
stock ShowAdminLevelCommands(playerid, const levelIndex)
{
    new adminLevel = PlayerInfo[playerid][pAdminLevel];
    new commandText[1024], title[64];
    new selectedLevel = 0;

    // Determine which level was selected based on index and player's admin level
    new currentIndex = 0;
    if(adminLevel >= 1 && currentIndex == levelIndex) selectedLevel = 1;
    else if(adminLevel >= 2 && ++currentIndex == levelIndex) selectedLevel = 2;
    else if(adminLevel >= 5 && ++currentIndex == levelIndex) selectedLevel = 5;
    else if(adminLevel >= 8 && ++currentIndex == levelIndex) selectedLevel = 8;
    else if(adminLevel >= 10 && ++currentIndex == levelIndex) selectedLevel = 10;

    // Format commands based on selected level
    switch(selectedLevel)
    {
        case 1:
        {
            // Check current page for level 1
            new page = PlayerAHelpPage[playerid];
            if(page < 1) page = 1;

            if(page == 1)
            {
                format(title, sizeof(title), "Level 1 - Basic Admin Commands (Page 1/2)");
                format(commandText, sizeof(commandText),
                    "{FFFFFF}Basic Admin Commands - Page 1:\n\n\
                    {FFFF00}/jetpack{FFFFFF} - Toggle jetpack on/off\n\
                    {FFFF00}/a [text]{FFFFFF} - Admin chat\n\
                    {FFFF00}/aduty{FFFFFF} - Toggle admin duty status\n\
                    {FFFF00}/adutytime{FFFFFF} - View your duty time statistics\n\
                    {FFFF00}/kick [playerid] [reason]{FFFFFF} - Kick player from server\n\
                    {FFFF00}/warn [playerid] [reason]{FFFFFF} - Warn player (max 10 warns)\n\
                    {FFFF00}/unwarn [playerid]{FFFFFF} - Remove one warn from player\n\
                    {FFFF00}/checkwarns [player_name] [page]{FFFFFF} - Check player's punishment records\n\
                    {FFFF00}/jail [playerid] [minute] [reason]{FFFFFF} - Jail player to OOC jail\n\
                    {FFFF00}/ojail [player_name] [minute] [reason]{FFFFFF} - Jail offline player\n\
                    {FFFF00}/owarn [player_name] [reason]{FFFFFF} - Warn offline player");
            }
            else
            {
                format(title, sizeof(title), "Level 1 - Basic Admin Commands (Page 2/2)");
                format(commandText, sizeof(commandText),
                    "{FFFFFF}Basic Admin Commands - Page 2:\n\n\
                    {FFFF00}/ban [playerid] [days] [reason]{FFFFFF} - Ban player (0=permanent)\n\
                    {FFFF00}/oban [player_name] [days] [reason]{FFFFFF} - Ban offline player\n\
                    {FFFF00}/unban [playerid]{FFFFFF} - Unban player\n\
                    {FFFF00}/ounban [player_name]{FFFFFF} - Unban offline player\n\
                    {FFFF00}/release [playerid]{FFFFFF} - Release player from jail\n\
                    {FFFF00}/checkstats [playerid]{FFFFFF} - Check player's statistics\n\
                    {FFFF00}/goto [playerid]{FFFFFF} - Teleport to player location\n\
                    {FFFF00}/forward{FFFFFF} - Move forward a short distance\n\
                    {FFFF00}/tothetop{FFFFFF} - Teleport to the top (upward)\n\n\
                    {FFFFFF}Note: Use {FFFF00}/mywarns [page]{FFFFFF} to view your punishment records with pagination.");
            }
        }
        case 2:
        {
            format(title, sizeof(title), "Level 2 - Player Management");
            format(commandText, sizeof(commandText),
                "{FFFFFF}Player Management Commands:\n\n\
                {FFFF00}/sethp [playerid] [health]{FFFFFF} - Set player health\n\
                {FFFF00}/setarmour [playerid] [armour]{FFFFFF} - Set player armour\n\
                {FFFF00}/aheal [playerid]{FFFFFF} - Heal/revive player to max HP\n\
                {FFFF00}/gethere [playerid]{FFFFFF} - Teleport player to your location\n\
                {FFFF00}/checkinventory [playerid]{FFFFFF} - Check player's inventory\n\
                {FFFF00}/setvw [playerid] [virtual_world]{FFFFFF} - Set player virtual world\n\
                {FFFF00}/setint [playerid] [interior]{FFFFFF} - Set player interior\n\n\
                {FFFFFF}Note: /aheal revives dead players with max HP (no penalties).");
        }
        case 5:
        {
            format(title, sizeof(title), "Level 5 - Advanced Commands");
            format(commandText, sizeof(commandText),
                "{FFFFFF}Advanced Admin Commands:\n\n\
                {FFFF00}/setskin [playerid] [skinid]{FFFFFF} - Change player skin\n\
                {FFFF00}/setweather [weather_id]{FFFFFF} - Change server weather (0-20)\n\
                {FFFF00}/giveweapon [playerid] [weaponid] [ammo]{FFFFFF} - Give custom weapon with ammo\n\
                {FFFF00}/removeinventory [playerid]{FFFFFF} - Clear player's inventory\n\n\
                {FFFFFF}Note: Skin ID must be between 0-311.\n\
                {FFFFFF}Weapon ID: 1-46, Ammo: 1-9999");
        }
        case 8:
        {
            format(title, sizeof(title), "Level 8 - Senior Admin Commands");
            format(commandText, sizeof(commandText),
                "{FFFFFF}Senior Admin Commands:\n\n\
                {FFFF00}/setmaxhp [playerid] [max_health]{FFFFFF} - Set player maximum health\n\
                {FFFF00}/gotoco [x] [y] [z] [interior]{FFFFFF} - Teleport to coordinates\n\n\
                {FFFFFF}Note: Max health range is 1.0 - 1000.0\n\
                {FFFFFF}Note: Interior range is 0 - 255");
        }
        case 10:
        {
            format(title, sizeof(title), "Level 10 - Head Admin Commands");
            format(commandText, sizeof(commandText),
                "{FFFFFF}Head Admin Commands:\n\n\
                {FFFF00}/setadminname [name]{FFFFFF} - Set custom admin name\n\
                {FFFF00}/resetadutytime [player_name]{FFFFFF} - Reset individual admin duty time\n\
                {FFFF00}/resetadutytimeall{FFFFFF} - Reset all admin duty times\n\
                {FFFF00}/makeadmin [playerid] [level]{FFFFFF} - Set player admin level\n\
                {FFFF00}/listadmin{FFFFFF} - View and manage admin list\n\
                {FFFF00}/removeallwarns [player_name]{FFFFFF} - Remove all player warns\n\
                {FFFF00}/refill [playerid]{FFFFFF} - Refill player's HBE to maximum\n\
                {FFFF00}/setbodypart [playerid] [bodypart] [condition]{FFFFFF} - Set body part condition\n\
                {FFFF00}/healbodyparts [playerid]{FFFFFF} - Heal all body parts to normal\n\n\
                {FFFFFF}Body Parts: 3=Torso, 4=Groin, 5=Left arm, 6=Right arm, 7=Left leg, 8=Right leg, 9=Head\n\
                {FFFFFF}Conditions: 0=Normal, 1=Dislocated, 2=Broken Bone, 3=Bruise");
        }
        default:
        {
            format(title, sizeof(title), "Error");
            format(commandText, sizeof(commandText), "{FF0000}Invalid level selection!");
        }
    }

    // Show different buttons based on level and page
    if(selectedLevel == 1)
    {
        new page = PlayerAHelpPage[playerid];
        if(page < 1) page = 1;

        if(page == 1)
        {
            Dialog_Show(playerid, AHelpLevel, DIALOG_STYLE_MSGBOX, title, commandText, "Next >>", "Back");
        }
        else
        {
            Dialog_Show(playerid, AHelpLevel, DIALOG_STYLE_MSGBOX, title, commandText, "<< Previous", "Back");
        }
    }
    else
    {
        Dialog_Show(playerid, AHelpLevel, DIALOG_STYLE_MSGBOX, title, commandText, "OK", "Back");
    }

    return 1;
}

// /resetadutytime command (Admin Level 10 only)
CMD:resetadutytime(playerid, params[])
{
    if(!PlayerInfo[playerid][pLogged]) return SendClientMessage(playerid, 0xFF0000FF, "ERROR: Anda belum login!");

    if(PlayerInfo[playerid][pAdminLevel] < 10)
        return SendClientMessage(playerid, 0xFF0000FF, "ERROR: Hanya admin level 10 yang bisa menggunakan command ini!");

    new targetName[MAX_PLAYER_NAME];
    if(sscanf(params, "s[24]", targetName))
        return SendClientMessage(playerid, 0x808080FF, "USAGE: /resetadutytime [nama_player]");

    // Log admin command
    LogAdminCommand(playerid, "/resetadutytime", targetName, "Duty time reset requested");

    // Query database to check if player exists and is admin
    new query[256];
    mysql_format(g_SQL, query, sizeof(query), "SELECT admin_level FROM `players` WHERE `username` = '%e'", targetName);
    mysql_tquery(g_SQL, query, "OnResetDutyTimeCheck", "is", playerid, targetName);

    return 1;
}

// Callback for checking player before resetting duty time
forward OnResetDutyTimeCheck(playerid, targetName[]);
public OnResetDutyTimeCheck(playerid, targetName[])
{
    new rows = cache_num_rows();

    if(!rows)
    {
        new message[128];
        format(message, sizeof(message), "ERROR: Player '%s' tidak ditemukan di database!", targetName);
        SendClientMessage(playerid, 0xFF0000FF, message);
        return 1;
    }

    new adminLevel;
    cache_get_value_int(0, "admin_level", adminLevel);

    if(adminLevel < 1)
    {
        new message[128];
        format(message, sizeof(message), "ERROR: Player '%s' bukan admin!", targetName);
        SendClientMessage(playerid, 0xFF0000FF, message);
        return 1;
    }

    // Reset duty time in database
    new query[256];
    mysql_format(g_SQL, query, sizeof(query), "UPDATE `players` SET `total_duty_time` = 0 WHERE `username` = '%e'", targetName);
    mysql_tquery(g_SQL, query, "OnResetDutyTimeComplete", "is", playerid, targetName);

    return 1;
}

// Callback for completing duty time reset
forward OnResetDutyTimeComplete(playerid, targetName[]);
public OnResetDutyTimeComplete(playerid, targetName[])
{
    // Check if target player is online to update their data and notify them
    new targetid = INVALID_PLAYER_ID;
    for(new i = 0; i < MAX_PLAYERS; i++)
    {
        if(IsPlayerConnected(i) && PlayerInfo[i][pLogged])
        {
            new onlineName[MAX_PLAYER_NAME];
            GetPlayerName(i, onlineName, sizeof(onlineName));
            if(strcmp(onlineName, targetName, true) == 0)
            {
                targetid = i;
                break;
            }
        }
    }

    // If player is online, update their memory data and notify them
    if(targetid != INVALID_PLAYER_ID)
    {
        PlayerInfo[targetid][pTotalDutyTime] = 0;
        PlayerInfo[targetid][pDutyStartTime] = gettime(); // Reset current session if on duty

        new message[128];
        format(message, sizeof(message), "Admin duty time Anda telah direset oleh Head Admin.");
        SendClientMessage(targetid, 0x00FF00FF, message);
    }

    // Notify the admin who performed the reset
    new message[128];
    format(message, sizeof(message), "Admin duty time %s berhasil direset oleh Head Admin.", targetName);
    SendClientMessage(playerid, 0x00FF00FF, message);

    return 1;
}

// /resetadutytimeall command (Admin Level 10 only)
CMD:resetadutytimeall(playerid, params[])
{
    if(!PlayerInfo[playerid][pLogged]) return SendClientMessage(playerid, 0xFF0000FF, "ERROR: Anda belum login!");

    if(PlayerInfo[playerid][pAdminLevel] < 10)
        return SendClientMessage(playerid, 0xFF0000FF, "ERROR: Hanya admin level 10 yang bisa menggunakan command ini!");

    // Log admin command
    LogAdminCommand(playerid, "/resetadutytimeall", "", "All admin duty times reset");

    // Give immediate feedback
    SendClientMessage(playerid, 0xFFFF00FF, "Memproses reset duty time semua admin...");

    // Reset all admin duty times in database
    new query[128];
    mysql_format(g_SQL, query, sizeof(query), "UPDATE `players` SET `total_duty_time` = 0 WHERE `admin_level` > 0");
    mysql_tquery(g_SQL, query, "OnResetAllDutyTimeComplete", "i", playerid);

    return 1;
}

// Callback for completing all duty time reset
forward OnResetAllDutyTimeComplete(playerid);
public OnResetAllDutyTimeComplete(playerid)
{
    new resetCount = 0;

    // Reset duty time for all online admins
    for(new i = 0; i < MAX_PLAYERS; i++)
    {
        if(IsPlayerConnected(i) && PlayerInfo[i][pLogged] && PlayerInfo[i][pAdminLevel] > 0)
        {
            PlayerInfo[i][pTotalDutyTime] = 0;
            PlayerInfo[i][pDutyStartTime] = gettime(); // Reset current session if on duty

            new message[128];
            format(message, sizeof(message), "Admin duty time Anda telah direset oleh Head Admin.");
            SendClientMessage(i, 0x00FF00FF, message);
            resetCount++;
        }
    }

    // Notify the admin who performed the reset
    new message[128];
    format(message, sizeof(message), "Semua admin duty time berhasil direset. (%d admin online telah dinotifikasi)", resetCount);
    SendClientMessage(playerid, 0x00FF00FF, message);

    return 1;
}

// Function to log admin commands with detailed information
stock LogAdminCommand(playerid, const command[], const target[] = "", const additional[] = "")
{
    if(!PlayerInfo[playerid][pLogged]) return 0;

    new playerName[MAX_PLAYER_NAME], playerIP[16], logMessage[512];
    GetPlayerName(playerid, playerName, sizeof(playerName));
    GetPlayerIp(playerid, playerIP, sizeof(playerIP));

    // Get current time for logging
    new year, month, day, hour, minute, second;
    getdate(year, month, day);
    gettime(hour, minute, second);

    // Format detailed log message
    if(strlen(target) > 0 && strlen(additional) > 0)
    {
        format(logMessage, sizeof(logMessage),
            "[%02d:%02d:%02d] ADMIN COMMAND: %s (Level %d, IP: %s) used %s on %s - %s",
            hour, minute, second, playerName, PlayerInfo[playerid][pAdminLevel],
            playerIP, command, target, additional);
    }
    else if(strlen(target) > 0)
    {
        format(logMessage, sizeof(logMessage),
            "[%02d:%02d:%02d] ADMIN COMMAND: %s (Level %d, IP: %s) used %s on %s",
            hour, minute, second, playerName, PlayerInfo[playerid][pAdminLevel],
            playerIP, command, target);
    }
    else
    {
        format(logMessage, sizeof(logMessage),
            "[%02d:%02d:%02d] ADMIN COMMAND: %s (Level %d, IP: %s) used %s",
            hour, minute, second, playerName, PlayerInfo[playerid][pAdminLevel],
            playerIP, command);
    }

    // Print to server console (samp-server.exe)
    print(logMessage);

    // Also log to server_log.txt
    printf("[ADMIN] %s", logMessage);

    return 1;
}

// Function to validate and set player health (respects max health)
stock SetPlayerHealthSafe(playerid, Float:health)
{
    if(health > PlayerInfo[playerid][pMaxHealth])
        health = PlayerInfo[playerid][pMaxHealth];

    if(health < 0.0)
        health = 0.0;

    PlayerInfo[playerid][pHealth] = health;
    SetPlayerHealth(playerid, health);
    return 1;
}

// /sethp command (Admin Level 2+)
CMD:sethp(playerid, params[])
{
    if(!PlayerInfo[playerid][pLogged]) return SendClientMessage(playerid, 0xFF0000FF, "ERROR: Anda belum login!");

    if(PlayerInfo[playerid][pAdminLevel] < 2)
        return SendClientMessage(playerid, 0xFF0000FF, "ERROR: Hanya admin level 2+ yang bisa menggunakan command ini!");

    new targetid, Float:health;
    if(sscanf(params, "uf", targetid, health))
        return SendClientMessage(playerid, 0x808080FF, "USAGE: /sethp [playerid] [health]");

    if(!IsPlayerConnected(targetid) || !PlayerInfo[targetid][pLogged])
        return SendClientMessage(playerid, 0xFF0000FF, "ERROR: Player tidak online atau belum login!");

    if(health < 0.0 || health > PlayerInfo[targetid][pMaxHealth])
    {
        new message[128];
        format(message, sizeof(message), "ERROR: Health harus 0.0 - %.1f (max health player)!", PlayerInfo[targetid][pMaxHealth]);
        SendClientMessage(playerid, 0xFF0000FF, message);
        return 1;
    }

    // Set player health
    SetPlayerHealthSafe(targetid, health);

    // Save to database immediately
    SavePlayerData(targetid);

    // Log admin command
    new logTargetName[MAX_PLAYER_NAME], logInfo[64];
    GetPlayerName(targetid, logTargetName, sizeof(logTargetName));
    format(logInfo, sizeof(logInfo), "Health set to %.1f", health);
    LogAdminCommand(playerid, "/sethp", logTargetName, logInfo);

    new targetName[MAX_PLAYER_NAME], adminName[32], message[128];
    GetPlayerName(targetid, targetName, sizeof(targetName));
    GetPlayerAdminName(playerid, adminName, sizeof(adminName));

    // Replace underscore with space in target name for display
    for(new i = 0; i < strlen(targetName); i++)
    {
        if(targetName[i] == '_') targetName[i] = ' ';
    }

    format(message, sizeof(message),
        "Health Anda telah diset menjadi %.1f oleh admin %s.",
        health, adminName);
    SendClientMessage(targetid, 0x00FF00FF, message);

    format(message, sizeof(message),
        "Anda telah mengset health %s menjadi %.1f.",
        targetName, health);
    SendClientMessage(playerid, 0x00FF00FF, message);

    return 1;
}

// /setarmour command (Admin Level 2+)
CMD:setarmour(playerid, params[])
{
    if(!PlayerInfo[playerid][pLogged]) return SendClientMessage(playerid, 0xFF0000FF, "ERROR: Anda belum login!");

    if(PlayerInfo[playerid][pAdminLevel] < 2)
        return SendClientMessage(playerid, 0xFF0000FF, "ERROR: Hanya admin level 2+ yang bisa menggunakan command ini!");

    new targetid, Float:armour;
    if(sscanf(params, "uf", targetid, armour))
        return SendClientMessage(playerid, 0x808080FF, "USAGE: /setarmour [playerid] [armour]");

    if(!IsPlayerConnected(targetid) || !PlayerInfo[targetid][pLogged])
        return SendClientMessage(playerid, 0xFF0000FF, "ERROR: Player tidak online atau belum login!");

    if(armour < 0.0 || armour > 100.0)
        return SendClientMessage(playerid, 0xFF0000FF, "ERROR: Armour harus 0.0 - 100.0!");

    // Set player armour
    PlayerInfo[targetid][pArmour] = armour;
    SetPlayerArmour(targetid, armour);

    // Save to database immediately
    SavePlayerData(targetid);

    // Log admin command
    new logTargetName[MAX_PLAYER_NAME], logInfo[64];
    GetPlayerName(targetid, logTargetName, sizeof(logTargetName));
    format(logInfo, sizeof(logInfo), "Armour set to %.1f", armour);
    LogAdminCommand(playerid, "/setarmour", logTargetName, logInfo);

    new targetName[MAX_PLAYER_NAME], adminName[32], message[128];
    GetPlayerName(targetid, targetName, sizeof(targetName));
    GetPlayerAdminName(playerid, adminName, sizeof(adminName));

    // Replace underscore with space in target name for display
    for(new i = 0; i < strlen(targetName); i++)
    {
        if(targetName[i] == '_') targetName[i] = ' ';
    }

    format(message, sizeof(message),
        "Armour Anda telah diset menjadi %.1f oleh admin %s.",
        armour, adminName);
    SendClientMessage(targetid, 0x00FF00FF, message);

    format(message, sizeof(message),
        "Anda telah mengset armour %s menjadi %.1f.",
        targetName, armour);
    SendClientMessage(playerid, 0x00FF00FF, message);

    return 1;
}

// /setmaxhp command (Admin Level 8+)
CMD:setmaxhp(playerid, params[])
{
    if(!PlayerInfo[playerid][pLogged]) return SendClientMessage(playerid, 0xFF0000FF, "ERROR: Anda belum login!");

    if(PlayerInfo[playerid][pAdminLevel] < 8)
        return SendClientMessage(playerid, 0xFF0000FF, "ERROR: Hanya admin level 8+ yang bisa menggunakan command ini!");

    new targetid, Float:maxhealth;
    if(sscanf(params, "uf", targetid, maxhealth))
        return SendClientMessage(playerid, 0x808080FF, "USAGE: /setmaxhp [playerid] [max_health]");

    if(!IsPlayerConnected(targetid) || !PlayerInfo[targetid][pLogged])
        return SendClientMessage(playerid, 0xFF0000FF, "ERROR: Player tidak online atau belum login!");

    if(maxhealth < 1.0 || maxhealth > 1000.0)
        return SendClientMessage(playerid, 0xFF0000FF, "ERROR: Max health harus 1.0 - 1000.0!");

    // Set max health
    PlayerInfo[targetid][pMaxHealth] = maxhealth;

    // Adjust current health if it exceeds new max health
    if(PlayerInfo[targetid][pHealth] > maxhealth)
    {
        SetPlayerHealthSafe(targetid, maxhealth);
    }

    // Save to database immediately
    SavePlayerData(targetid);

    // Log admin command
    new targetName[MAX_PLAYER_NAME], logInfo[64];
    GetPlayerName(targetid, targetName, sizeof(targetName));
    format(logInfo, sizeof(logInfo), "Max health set to %.1f", maxhealth);
    LogAdminCommand(playerid, "/setmaxhp", targetName, logInfo);

    new adminName[32], message[128];
    GetPlayerName(targetid, targetName, sizeof(targetName));
    GetPlayerAdminName(playerid, adminName, sizeof(adminName));

    // Replace underscore with space in target name for display
    for(new i = 0; i < strlen(targetName); i++)
    {
        if(targetName[i] == '_') targetName[i] = ' ';
    }

    format(message, sizeof(message),
        "Max health Anda telah diset menjadi %.1f oleh admin %s.",
        maxhealth, adminName);
    SendClientMessage(targetid, 0x00FF00FF, message);

    format(message, sizeof(message),
        "Anda telah mengset max health %s menjadi %.1f.",
        targetName, maxhealth);
    SendClientMessage(playerid, 0x00FF00FF, message);

    return 1;
}

// /setskin command (Admin Level 5+)
CMD:setskin(playerid, params[])
{
    if(!PlayerInfo[playerid][pLogged]) return SendClientMessage(playerid, 0xFF0000FF, "ERROR: Anda belum login!");

    if(PlayerInfo[playerid][pAdminLevel] < 5)
        return SendClientMessage(playerid, 0xFF0000FF, "ERROR: Hanya admin level 5+ yang bisa menggunakan command ini!");

    new targetid, skinid;
    if(sscanf(params, "ud", targetid, skinid))
        return SendClientMessage(playerid, 0x808080FF, "USAGE: /setskin [playerid] [skinid]");

    if(!IsPlayerConnected(targetid) || !PlayerInfo[targetid][pLogged])
        return SendClientMessage(playerid, 0xFF0000FF, "ERROR: Player tidak online atau belum login!");

    if(skinid < 0 || skinid > 311)
        return SendClientMessage(playerid, 0xFF0000FF, "ERROR: Skin ID harus 0-311!");

    // Set player skin
    SetPlayerSkin(targetid, skinid);
    PlayerInfo[targetid][pSkin] = skinid;

    // Save to database immediately
    SavePlayerData(targetid);

    // Log admin command
    new targetName[MAX_PLAYER_NAME], logInfo[64];
    GetPlayerName(targetid, targetName, sizeof(targetName));
    format(logInfo, sizeof(logInfo), "Skin set to ID %d", skinid);
    LogAdminCommand(playerid, "/setskin", targetName, logInfo);

    new adminName[32], message[128];
    GetPlayerName(targetid, targetName, sizeof(targetName));
    GetPlayerAdminName(playerid, adminName, sizeof(adminName));

    // Replace underscore with space in target name for display
    for(new i = 0; i < strlen(targetName); i++)
    {
        if(targetName[i] == '_') targetName[i] = ' ';
    }

    format(message, sizeof(message),
        "Skin Anda telah diubah menjadi ID %d oleh admin %s.",
        skinid, adminName);
    SendClientMessage(targetid, 0x00FF00FF, message);

    format(message, sizeof(message),
        "Anda telah mengubah skin %s menjadi ID %d.",
        targetName, skinid);
    SendClientMessage(playerid, 0x00FF00FF, message);

    return 1;
}

// /listadmin command (Admin Level 10 only)
CMD:listadmin(playerid, params[])
{
    if(!PlayerInfo[playerid][pLogged]) return SendClientMessage(playerid, 0xFF0000FF, "ERROR: Anda belum login!");

    if(PlayerInfo[playerid][pAdminLevel] < 10)
        return SendClientMessage(playerid, 0xFF0000FF, "ERROR: Hanya admin level 10 yang bisa menggunakan command ini!");

    // Log admin command
    LogAdminCommand(playerid, "/listadmin", "", "Admin list accessed");

    // Reset to first page
    PlayerInfo[playerid][pAdminListPage] = 0;

    // Query database for all admins
    new query[256];
    mysql_format(g_SQL, query, sizeof(query), "SELECT username, admin_name, admin_level, total_duty_time, id FROM `players` WHERE `admin_level` > 0 ORDER BY `admin_level` DESC, `username` ASC");
    mysql_tquery(g_SQL, query, "OnLoadAdminList", "i", playerid);

    return 1;
}

// Callback for loading admin list
forward OnLoadAdminList(playerid);
public OnLoadAdminList(playerid)
{
    new rows = cache_num_rows();

    if(!rows)
    {
        SendClientMessage(playerid, 0xFF0000FF, "Tidak ada admin yang ditemukan di database!");
        return 1;
    }

    new adminList[2048], tempStr[128];
    new currentPage = PlayerInfo[playerid][pAdminListPage];
    new startIndex = currentPage * 10;
    new endIndex = startIndex + 10;
    if(endIndex > rows) endIndex = rows;

    // Header
    strcat(adminList, "Nama Player\tNama Admin\tLevel\tTotal Duty\n");

    // Add admin data for current page
    for(new i = startIndex; i < endIndex; i++)
    {
        new username[MAX_PLAYER_NAME], adminName[32], adminLevel, totalDutyTime;

        cache_get_value(i, "username", username, sizeof(username));
        cache_get_value(i, "admin_name", adminName, sizeof(adminName));
        cache_get_value_int(i, "admin_level", adminLevel);
        cache_get_value_int(i, "total_duty_time", totalDutyTime);

        // Replace underscore with space in username
        for(new j = 0; j < strlen(username); j++)
        {
            if(username[j] == '_') username[j] = ' ';
        }

        // Format duty time as HH:MM:SS
        new hours = totalDutyTime / 3600;
        new minutes = (totalDutyTime % 3600) / 60;
        new seconds = totalDutyTime % 60;

        // Use default admin name if custom name is empty
        if(strlen(adminName) == 0)
        {
            GetDefaultAdminName(adminLevel, adminName, sizeof(adminName));
        }

        format(tempStr, sizeof(tempStr), "%s\t%s\t%d\t%02d:%02d:%02d\n",
            username, adminName, adminLevel, hours, minutes, seconds);
        strcat(adminList, tempStr);
    }

    // Add navigation if needed
    new totalPages = (rows + 9) / 10; // Round up division
    if(totalPages > 1)
    {
        if(currentPage > 0 && currentPage < totalPages - 1)
        {
            strcat(adminList, "<< Back\t-\t-\tNext >>");
        }
        else if(currentPage == 0 && totalPages > 1)
        {
            strcat(adminList, "-\t-\t-\tNext >>");
        }
        else if(currentPage == totalPages - 1)
        {
            strcat(adminList, "<< Back\t-\t-\t-");
        }
    }

    new title[64];
    format(title, sizeof(title), "Admin List - Page %d/%d", currentPage + 1, totalPages);

    Dialog_Show(playerid, ListAdmin, DIALOG_STYLE_TABLIST_HEADERS, title, adminList, "Select", "Close");

    return 1;
}

// Callback for selecting admin for management
forward OnSelectAdminForManagement(playerid);
public OnSelectAdminForManagement(playerid)
{
    new rows = cache_num_rows();

    if(!rows)
    {
        SendClientMessage(playerid, 0xFF0000FF, "ERROR: Admin tidak ditemukan!");
        return 1;
    }

    new adminID;
    cache_get_value_int(0, "id", adminID);
    PlayerInfo[playerid][pSelectedAdminID] = adminID;

    ShowAdminManagementMenu(playerid);
    return 1;
}

// Function to show admin management menu
stock ShowAdminManagementMenu(playerid)
{
    new query[256];
    mysql_format(g_SQL, query, sizeof(query), "SELECT username, admin_name, admin_level FROM `players` WHERE `id` = %d", PlayerInfo[playerid][pSelectedAdminID]);
    mysql_tquery(g_SQL, query, "OnShowAdminManagementMenu", "i", playerid);
    return 1;
}

// Callback for showing admin management menu
forward OnShowAdminManagementMenu(playerid);
public OnShowAdminManagementMenu(playerid)
{
    new rows = cache_num_rows();

    if(!rows)
    {
        SendClientMessage(playerid, 0xFF0000FF, "ERROR: Admin tidak ditemukan!");
        return 1;
    }

    new username[MAX_PLAYER_NAME], adminName[32], adminLevel;
    cache_get_value(0, "username", username, sizeof(username));
    cache_get_value(0, "admin_name", adminName, sizeof(adminName));
    cache_get_value_int(0, "admin_level", adminLevel);

    // Replace underscore with space in username
    for(new i = 0; i < strlen(username); i++)
    {
        if(username[i] == '_') username[i] = ' ';
    }

    // Use default admin name if custom name is empty
    if(strlen(adminName) == 0)
    {
        GetDefaultAdminName(adminLevel, adminName, sizeof(adminName));
    }

    new menuText[256];
    format(menuText, sizeof(menuText),
        "Set Admin Level\nKick Admin (Remove Level)");

    new title[128];
    format(title, sizeof(title), "Manage Admin: %s (Level %d)", username, adminLevel);

    Dialog_Show(playerid, AdminManage, DIALOG_STYLE_LIST, title, menuText, "Select", "Back");

    return 1;
}

// Callback for admin level update
forward OnAdminLevelUpdated(playerid, newLevel);
public OnAdminLevelUpdated(playerid, newLevel)
{
    new query[256];
    mysql_format(g_SQL, query, sizeof(query), "SELECT username FROM `players` WHERE `id` = %d", PlayerInfo[playerid][pSelectedAdminID]);
    mysql_tquery(g_SQL, query, "OnAdminLevelUpdateComplete", "ii", playerid, newLevel);
    return 1;
}

// Callback for completing admin level update
forward OnAdminLevelUpdateComplete(playerid, newLevel);
public OnAdminLevelUpdateComplete(playerid, newLevel)
{
    new rows = cache_num_rows();

    if(!rows)
    {
        SendClientMessage(playerid, 0xFF0000FF, "ERROR: Admin tidak ditemukan!");
        return 1;
    }

    new username[MAX_PLAYER_NAME];
    cache_get_value(0, "username", username, sizeof(username));

    // Check if target player is online to update their data
    new targetid = INVALID_PLAYER_ID;
    for(new i = 0; i < MAX_PLAYERS; i++)
    {
        if(IsPlayerConnected(i) && PlayerInfo[i][pLogged])
        {
            new onlineName[MAX_PLAYER_NAME];
            GetPlayerName(i, onlineName, sizeof(onlineName));
            if(strcmp(onlineName, username, true) == 0)
            {
                targetid = i;
                break;
            }
        }
    }

    // If player is online, update their memory data and notify them
    if(targetid != INVALID_PLAYER_ID)
    {
        PlayerInfo[targetid][pAdminLevel] = newLevel;

        if(newLevel > 0)
        {
            GetDefaultAdminName(newLevel, PlayerInfo[targetid][pAdminName], 32);
            new message[128];
            format(message, sizeof(message), "Admin level Anda telah diubah menjadi %d oleh Head Admin.", newLevel);
            SendClientMessage(targetid, 0x00FF00FF, message);
        }
        else
        {
            PlayerInfo[targetid][pAdminName][0] = 0;
            PlayerInfo[targetid][pAdminDuty] = 0;
            PlayerInfo[targetid][pDutyStartTime] = 0;
            UpdatePlayerTabName(targetid); // Reset name and color
            SendClientMessage(targetid, 0xFF0000FF, "Admin level Anda telah dihapus oleh Head Admin.");
        }
    }

    // Notify the admin who performed the action
    new message[128];
    if(newLevel > 0)
    {
        format(message, sizeof(message), "Admin level %s berhasil diubah menjadi %d.", username, newLevel);
    }
    else
    {
        format(message, sizeof(message), "Admin level %s berhasil dihapus.", username);
    }
    SendClientMessage(playerid, 0x00FF00FF, message);

    // Go back to admin list
    new query[256];
    mysql_format(g_SQL, query, sizeof(query), "SELECT username, admin_name, admin_level, total_duty_time, id FROM `players` WHERE `admin_level` > 0 ORDER BY `admin_level` DESC, `username` ASC");
    mysql_tquery(g_SQL, query, "OnLoadAdminList", "i", playerid);

    return 1;
}

// /makeadmin command (Admin Level 10 only)
CMD:makeadmin(playerid, params[])
{
    if(!PlayerInfo[playerid][pLogged]) return SendClientMessage(playerid, 0xFF0000FF, "ERROR: Anda belum login!");

    if(PlayerInfo[playerid][pAdminLevel] < 10)
        return SendClientMessage(playerid, 0xFF0000FF, "ERROR: Hanya admin level 10 yang bisa menggunakan command ini!");

    new targetid, level;
    if(sscanf(params, "ud", targetid, level))
        return SendClientMessage(playerid, 0x808080FF, "USAGE: /makeadmin [playerid] [level 1-10]");

    if(!IsPlayerConnected(targetid) || !PlayerInfo[targetid][pLogged])
        return SendClientMessage(playerid, 0xFF0000FF, "ERROR: Player tidak online atau belum login!");

    if(level < 0 || level > 10)
        return SendClientMessage(playerid, 0xFF0000FF, "ERROR: Admin level harus 0-10!");

    PlayerInfo[targetid][pAdminLevel] = level;

    // Set default admin name based on level
    if(level > 0)
    {
        GetDefaultAdminName(level, PlayerInfo[targetid][pAdminName], 32);
    }
    else
    {
        PlayerInfo[targetid][pAdminName][0] = 0; // Clear admin name
    }

    // Save to database immediately
    SavePlayerData(targetid);

    // Log admin command
    new targetName[MAX_PLAYER_NAME], logInfo[64];
    GetPlayerName(targetid, targetName, sizeof(targetName));
    if(level > 0)
    {
        format(logInfo, sizeof(logInfo), "Admin level set to %d (%s)", level, PlayerInfo[targetid][pAdminName]);
    }
    else
    {
        format(logInfo, sizeof(logInfo), "Admin level removed (set to 0)");
    }
    LogAdminCommand(playerid, "/makeadmin", targetName, logInfo);

    new message[128];

    if(level > 0)
    {
        format(message, sizeof(message),
            "Anda telah dijadikan admin level %d (%s) oleh administrator.",
            level, PlayerInfo[targetid][pAdminName]);
        SendClientMessage(targetid, 0x00FF00FF, message);

        format(message, sizeof(message),
            "Anda telah memberikan admin level %d (%s) kepada %s.",
            level, PlayerInfo[targetid][pAdminName], targetName);
        SendClientMessage(playerid, 0x00FF00FF, message);
    }
    else
    {
        SendClientMessage(targetid, 0xFF0000FF, "Admin level Anda telah dihapus.");
        format(message, sizeof(message),
            "Anda telah menghapus admin level %s.", targetName);
        SendClientMessage(playerid, 0x00FF00FF, message);
    }

    return 1;
}

// /b command (OOC chat)
CMD:b(playerid, params[])
{
    if(!PlayerInfo[playerid][pLogged]) return SendClientMessage(playerid, 0xFF0000FF, "ERROR: Anda belum login!");

    if(isnull(params)) return SendClientMessage(playerid, 0x808080FF, "USAGE: /b [text]");

    new string[256], name[MAX_PLAYER_NAME];
    GetPlayerDisplayName(playerid, name, sizeof(name));

    // Use cyan name color if jailed, red if admin on duty
    if(PlayerInfo[playerid][pJailed])
    {
        format(string, sizeof(string), "(( {00FFFF}%s{E6E6FA}: %s ))", name, params);
    }
    else if(PlayerInfo[playerid][pAdminDuty] && PlayerInfo[playerid][pAdminLevel] > 0)
    {
        format(string, sizeof(string), "(( {FF0000}%s{E6E6FA}: %s ))", name, params);
    }
    else
    {
        format(string, sizeof(string), "(( %s: %s ))", name, params);
    }

    // Send to nearby players (30 meter radius)
    new Float:x, Float:y, Float:z;
    GetPlayerPos(playerid, x, y, z);

    for(new i = 0; i < MAX_PLAYERS; i++)
    {
        if(IsPlayerConnected(i) && PlayerInfo[i][pLogged])
        {
            if(GetPlayerDistanceFromPoint(i, x, y, z) <= 30.0)
            {
                SendClientMessage(i, 0xE6E6FAFF, string); // Light purple color
            }
        }
    }

    return 1;
}

// /ame command (text above head with 3D text)
CMD:ame(playerid, params[])
{
    if(!PlayerInfo[playerid][pLogged]) return SendClientMessage(playerid, 0xFF0000FF, "ERROR: Anda belum login!");

    // Disable RP commands when admin is on duty
    if(PlayerInfo[playerid][pAdminDuty] && PlayerInfo[playerid][pAdminLevel] > 0)
        return SendClientMessage(playerid, 0xFF0000FF, "ERROR: Roleplay commands disabled saat admin duty! Gunakan /b untuk OOC chat.");

    if(isnull(params)) return SendClientMessage(playerid, 0x808080FF, "USAGE: /ame [text]");

    new string[256], name[MAX_PLAYER_NAME];
    GetPlayerDisplayName(playerid, name, sizeof(name));

    format(string, sizeof(string), "* %s %s", name, params);

    // Send to nearby players as chat
    new Float:x, Float:y, Float:z;
    GetPlayerPos(playerid, x, y, z);

    for(new i = 0; i < MAX_PLAYERS; i++)
    {
        if(IsPlayerConnected(i) && PlayerInfo[i][pLogged])
        {
            if(GetPlayerDistanceFromPoint(i, x, y, z) <= 30.0)
            {
                SendClientMessage(i, 0xC2A2DAFF, string);
            }
        }
    }

    // Create 3D text above player's head
    new Text3D:ameText = Create3DTextLabel(params, 0xC2A2DAFF, 0.0, 0.0, 0.3, 30.0, 0, true);
    Attach3DTextLabelToPlayer(ameText, playerid, 0.0, 0.0, 0.3);

    // Delete the 3D text after 5 seconds
    new funcname[] = "DeleteAmeText";
    new formatstr[] = "i";
    SetTimerEx(funcname, 5000, false, formatstr, _:ameText);

    return 1;
}

// Function to delete ame text after delay
forward DeleteAmeText(Text3D:textid);
public DeleteAmeText(Text3D:textid)
{
    Delete3DTextLabel(textid);
    return 1;
}

// /ado command (3D text)
CMD:ado(playerid, params[])
{
    if(!PlayerInfo[playerid][pLogged]) return SendClientMessage(playerid, 0xFF0000FF, "ERROR: Anda belum login!");

    // Disable RP commands when admin is on duty
    if(PlayerInfo[playerid][pAdminDuty] && PlayerInfo[playerid][pAdminLevel] > 0)
        return SendClientMessage(playerid, 0xFF0000FF, "ERROR: Roleplay commands disabled saat admin duty! Gunakan /b untuk OOC chat.");

    if(isnull(params))
    {
        // Delete existing 3D text if no params
        if(PlayerInfo[playerid][pAdoText] != Text3D:INVALID_3DTEXT_ID)
        {
            Delete3DTextLabel(PlayerInfo[playerid][pAdoText]);
            PlayerInfo[playerid][pAdoText] = Text3D:INVALID_3DTEXT_ID;
            SendClientMessage(playerid, 0x00FF00FF, "3D Text dihapus.");
        }
        else
        {
            SendClientMessage(playerid, 0x808080FF, "USAGE: /ado [text] - atau /ado tanpa text untuk menghapus");
        }
        return 1;
    }

    // Delete existing 3D text first
    if(PlayerInfo[playerid][pAdoText] != Text3D:INVALID_3DTEXT_ID)
    {
        Delete3DTextLabel(PlayerInfo[playerid][pAdoText]);
    }

    // Create new 3D text
    new Float:x, Float:y, Float:z;
    GetPlayerPos(playerid, x, y, z);

    new string[256], name[MAX_PLAYER_NAME];
    GetPlayerDisplayName(playerid, name, sizeof(name));

    format(string, sizeof(string), "* %s\n(( %s ))", params, name);

    PlayerInfo[playerid][pAdoText] = Create3DTextLabel(string, 0xC2A2DAFF, x, y, z + 0.5, 30.0, 0, false);

    SendClientMessage(playerid, 0x00FF00FF, "3D Text dibuat. Gunakan /ado tanpa text untuk menghapus.");

    return 1;
}

// /l command (low/pelan)
CMD:l(playerid, params[])
{
    if(!PlayerInfo[playerid][pLogged]) return SendClientMessage(playerid, 0xFF0000FF, "ERROR: Anda belum login!");

    // Disable RP commands when admin is on duty
    if(PlayerInfo[playerid][pAdminDuty] && PlayerInfo[playerid][pAdminLevel] > 0)
        return SendClientMessage(playerid, 0xFF0000FF, "ERROR: Roleplay commands disabled saat admin duty! Gunakan /b untuk OOC chat.");

    if(isnull(params)) return SendClientMessage(playerid, 0x808080FF, "USAGE: /l [text]");

    new string[256], name[MAX_PLAYER_NAME];
    GetPlayerDisplayName(playerid, name, sizeof(name));

    format(string, sizeof(string), "%s says [low]: %s", name, params);

    // Send to nearby players (5 meter radius - realistic low voice distance)
    new Float:x, Float:y, Float:z;
    GetPlayerPos(playerid, x, y, z);

    for(new i = 0; i < MAX_PLAYERS; i++)
    {
        if(IsPlayerConnected(i) && PlayerInfo[i][pLogged])
        {
            if(GetPlayerDistanceFromPoint(i, x, y, z) <= 5.0)
            {
                SendClientMessage(i, 0xC0C0C0FF, string);
            }
        }
    }

    return 1;
}

// /s command (shout/teriak)
CMD:s(playerid, params[])
{
    if(!PlayerInfo[playerid][pLogged]) return SendClientMessage(playerid, 0xFF0000FF, "ERROR: Anda belum login!");

    // Disable RP commands when admin is on duty or jailed
    if(PlayerInfo[playerid][pAdminDuty] && PlayerInfo[playerid][pAdminLevel] > 0)
        return SendClientMessage(playerid, 0xFF0000FF, "ERROR: Roleplay commands disabled saat admin duty! Gunakan /b untuk OOC chat.");

    if(PlayerInfo[playerid][pJailed])
        return SendClientMessage(playerid, 0xFF0000FF, "ERROR: Anda tidak bisa menggunakan command IC saat di jail! Gunakan /b untuk OOC chat.");

    if(isnull(params)) return SendClientMessage(playerid, 0x808080FF, "USAGE: /s [text]");

    new string[256], name[MAX_PLAYER_NAME];
    GetPlayerDisplayName(playerid, name, sizeof(name));

    format(string, sizeof(string), "%s shouts: %s!!", name, params);

    // Send to nearby players (25 meter radius - realistic shouting distance)
    new Float:x, Float:y, Float:z;
    GetPlayerPos(playerid, x, y, z);

    for(new i = 0; i < MAX_PLAYERS; i++)
    {
        if(IsPlayerConnected(i) && PlayerInfo[i][pLogged])
        {
            if(GetPlayerDistanceFromPoint(i, x, y, z) <= 25.0)
            {
                SendClientMessage(i, 0xFFFFFFFF, string);
            }
        }
    }

    return 1;
}

// /w command (whisper to specific player)
CMD:w(playerid, params[])
{
    if(!PlayerInfo[playerid][pLogged]) return SendClientMessage(playerid, 0xFF0000FF, "ERROR: Anda belum login!");

    // Disable RP commands when admin is on duty or jailed
    if(PlayerInfo[playerid][pAdminDuty] && PlayerInfo[playerid][pAdminLevel] > 0)
        return SendClientMessage(playerid, 0xFF0000FF, "ERROR: Roleplay commands disabled saat admin duty! Gunakan /b untuk OOC chat.");

    if(PlayerInfo[playerid][pJailed])
        return SendClientMessage(playerid, 0xFF0000FF, "ERROR: Anda tidak bisa menggunakan command IC saat di jail! Gunakan /b untuk OOC chat.");

    new targetid, message[128];
    if(sscanf(params, "us[128]", targetid, message))
        return SendClientMessage(playerid, 0x808080FF, "USAGE: /w [playerid/name] [message]");

    if(!IsPlayerConnected(targetid) || !PlayerInfo[targetid][pLogged])
        return SendClientMessage(playerid, 0xFF0000FF, "ERROR: Player tidak online atau belum login!");

    // Check distance (must be very close to whisper)
    new Float:px, Float:py, Float:pz, Float:tx, Float:ty, Float:tz;
    GetPlayerPos(playerid, px, py, pz);
    GetPlayerPos(targetid, tx, ty, tz);

    new Float:distance = GetPlayerDistanceFromPoint(playerid, tx, ty, tz);
    if(distance > 3.0)
        return SendClientMessage(playerid, 0xFF0000FF, "ERROR: Player terlalu jauh untuk dibisikkan! (max 3 meter)");

    new senderName[MAX_PLAYER_NAME], targetName[MAX_PLAYER_NAME];
    GetPlayerDisplayName(playerid, senderName, sizeof(senderName));
    GetPlayerDisplayName(targetid, targetName, sizeof(targetName));

    new string[256];

    // Message to sender
    format(string, sizeof(string), "You whisper to %s: %s", targetName, message);
    SendClientMessage(playerid, 0x9370DBFF, string);

    // Message to target
    format(string, sizeof(string), "%s whispers to you: %s", senderName, message);
    SendClientMessage(targetid, 0x9370DBFF, string);

    // Message to nearby players (they see the action but not the message)
    format(string, sizeof(string), "* %s whispers something to %s", senderName, targetName);

    new Float:x, Float:y, Float:z;
    GetPlayerPos(playerid, x, y, z);

    for(new i = 0; i < MAX_PLAYERS; i++)
    {
        if(IsPlayerConnected(i) && PlayerInfo[i][pLogged] && i != playerid && i != targetid)
        {
            if(GetPlayerDistanceFromPoint(i, x, y, z) <= 5.0) // Reduced from 10m to 5m
            {
                SendClientMessage(i, 0xC2A2DAFF, string);
            }
        }
    }

    return 1;
}

// ========== DEATH SYSTEM FUNCTIONS ==========

// Function to save player death status to database
stock SavePlayerDeathStatus(playerid)
{
    if(!PlayerInfo[playerid][pLogged]) return 0;

    new query[512];
    mysql_format(g_SQL, query, sizeof(query),
        "UPDATE `players` SET `is_dead` = %d, `death_x` = %f, `death_y` = %f, `death_z` = %f, `death_angle` = %f WHERE `id` = %d",
        PlayerInfo[playerid][pIsDead] ? 1 : 0,
        PlayerInfo[playerid][pDeathX],
        PlayerInfo[playerid][pDeathY],
        PlayerInfo[playerid][pDeathZ],
        PlayerInfo[playerid][pDeathAngle],
        PlayerInfo[playerid][pID]
    );
    mysql_tquery(g_SQL, query);
    return 1;
}

// Function to load player death status from database
stock LoadPlayerDeathStatus(playerid)
{
    if(!PlayerInfo[playerid][pLogged]) return 0;

    new query[256];
    mysql_format(g_SQL, query, sizeof(query),
        "SELECT `is_dead`, `death_x`, `death_y`, `death_z`, `death_angle` FROM `players` WHERE `id` = %d",
        PlayerInfo[playerid][pID]
    );
    mysql_tquery(g_SQL, query, "OnLoadPlayerDeathStatus", "i", playerid);
    return 1;
}

// Callback for loading death status
forward OnLoadPlayerDeathStatus(playerid);
public OnLoadPlayerDeathStatus(playerid)
{
    if(!IsPlayerConnected(playerid) || !PlayerInfo[playerid][pLogged]) return 0;

    new rows = cache_num_rows();
    if(rows > 0)
    {
        new is_dead;
        cache_get_value_int(0, "is_dead", is_dead);

        if(is_dead == 1)
        {
            // Player was dead when they disconnected
            PlayerInfo[playerid][pIsDead] = true;
            cache_get_value_float(0, "death_x", PlayerInfo[playerid][pDeathX]);
            cache_get_value_float(0, "death_y", PlayerInfo[playerid][pDeathY]);
            cache_get_value_float(0, "death_z", PlayerInfo[playerid][pDeathZ]);
            cache_get_value_float(0, "death_angle", PlayerInfo[playerid][pDeathAngle]);

            // Start new 3-minute death timer (reset timer on reconnect)
            new funcname[] = "OnDeathTimerEnd";
            new formatstr[] = "i";
            PlayerInfo[playerid][pDeathTimer] = SetTimerEx(funcname, 180000, false, formatstr, playerid);
        }
    }
    return 1;
}



// Function to apply death effects (screen shake only, no red screen) - kept for compatibility
stock ApplyDeathEffects(playerid, bool:showMessage = true)
{
    // Start screen shake effect (drunk effect)
    StartDeathScreenShake(playerid);

    // Freeze player
    TogglePlayerControllable(playerid, false);

    // Set health to prevent further damage
    SetPlayerHealth(playerid, 1.0);

    // Send death message only if requested (to prevent spam)
    if(showMessage)
    {
        SendClientMessage(playerid, 0xFF0000FF, "Anda telah mati! Tunggu 3 menit atau gunakan /acceptdeath untuk hidup kembali.");
        SendClientMessage(playerid, 0xFFFF00FF, "Selama mati, Anda tidak akan menerima damage dan layar akan bergoyang.");
        SendClientMessage(playerid, 0x00FF00FF, "Anda akan otomatis respawn di lokasi kematian dalam beberapa detik.");
    }

    return 1;
}



// Function to start screen shake effect during death (drunk-like effect)
stock StartDeathScreenShake(playerid)
{
    // Stop existing shake timer if running
    if(PlayerInfo[playerid][pShakeTimer] != 0)
    {
        KillTimer(PlayerInfo[playerid][pShakeTimer]);
    }

    // Apply drunk effect to player for realistic drunk-like camera movement
    SetPlayerDrunkLevel(playerid, 50000); // High drunk level for intense effect

    // Also add manual camera shake for extra effect
    new funcname[] = "DeathScreenShake";
    new formatstr[] = "i";
    PlayerInfo[playerid][pShakeTimer] = SetTimerEx(funcname, 200, true, formatstr, playerid);
    return 1;
}

// Timer function for drunk-like screen shake effect
forward DeathScreenShake(playerid);
public DeathScreenShake(playerid)
{
    if(!IsPlayerConnected(playerid) || !PlayerInfo[playerid][pIsDead])
    {
        // Stop timer and reset drunk level if player disconnected or no longer dead
        if(PlayerInfo[playerid][pShakeTimer] != 0)
        {
            KillTimer(PlayerInfo[playerid][pShakeTimer]);
            PlayerInfo[playerid][pShakeTimer] = 0;
        }
        SetPlayerDrunkLevel(playerid, 0); // Reset drunk level
        return 0;
    }

    // Maintain high drunk level for realistic drunk-like camera shake
    // This creates the swaying, unsteady camera movement like being drunk
    SetPlayerDrunkLevel(playerid, 50000);

    return 1;
}

// Timer function to keep death animation playing
forward KeepDeathAnimation(playerid);
public KeepDeathAnimation(playerid)
{
    if(!IsPlayerConnected(playerid) || !PlayerInfo[playerid][pIsDead])
    {
        // Stop timer if player disconnected or no longer dead
        if(PlayerInfo[playerid][pAnimTimer] != 0)
        {
            KillTimer(PlayerInfo[playerid][pAnimTimer]);
            PlayerInfo[playerid][pAnimTimer] = 0;
        }
        return 0;
    }

    // Re-apply death animation to keep it playing - crackhead idle
    new animlib[32] = "CRACK";
    new animname[32] = "crckidle2";
    ApplyAnimation(playerid, animlib, animname, 4.0, 1, 0, 0, 1, 0);

    return 1;
}

// Function to apply death state like the example script
forward ApplyDeathState(playerid);
public ApplyDeathState(playerid)
{
    if(!IsPlayerConnected(playerid) || !PlayerInfo[playerid][pIsDead])
        return 1;

    // Spawn player to apply death effects
    SpawnPlayer(playerid);

    return 1;
}

// Function to apply death mode immediately at death location
forward ApplyDeathModeImmediate(playerid);
public ApplyDeathModeImmediate(playerid)
{
    if(!IsPlayerConnected(playerid) || !PlayerInfo[playerid][pIsDead])
        return 1;

    // Posisi dan skin sudah di-set di OnPlayerSpawn, jadi langsung apply effects

    // Set health to 1 to prevent further damage
    SetPlayerHealth(playerid, 1.0);

    // Apply death animation - crackhead idle lying down
    new animlib[32] = "CRACK";
    new animname[32] = "crckidle2";
    ApplyAnimation(playerid, animlib, animname, 4.0, 1, 0, 0, 1, 0);

    // Start screen shake (shake)
    StartDeathScreenShake(playerid);

    // Start animation loop timer (more frequent to keep player lying down)
    new funcname[] = "KeepDeathAnimation";
    new formatstr[] = "i";
    PlayerInfo[playerid][pAnimTimer] = SetTimerEx(funcname, 2000, true, formatstr, playerid);
    return 1;
}



// Function to spawn player at death location with death effects
forward SpawnPlayerAtDeathLocation(playerid);
public SpawnPlayerAtDeathLocation(playerid)
{
    if(!IsPlayerConnected(playerid) || !PlayerInfo[playerid][pIsDead])
        return 1;

    // Spawn player at death location
    SpawnPlayer(playerid);

    return 1;
}

// Timer callback when 3 minutes death timer ends
forward OnDeathTimerEnd(playerid);
public OnDeathTimerEnd(playerid)
{
    if(!IsPlayerConnected(playerid) || !PlayerInfo[playerid][pIsDead])
        return 1;

    // Stop screen shake and reset drunk effect
    if(PlayerInfo[playerid][pShakeTimer] != 0)
    {
        KillTimer(PlayerInfo[playerid][pShakeTimer]);
        PlayerInfo[playerid][pShakeTimer] = 0;
    }

    // Stop animation timer if running
    if(PlayerInfo[playerid][pAnimTimer] != 0)
    {
        KillTimer(PlayerInfo[playerid][pAnimTimer]);
        PlayerInfo[playerid][pAnimTimer] = 0;
    }

    SetCameraBehindPlayer(playerid);
    SetPlayerDrunkLevel(playerid, 0); // Reset drunk effect - stop screen shake

    // Show accept death message (reduced spam)
    SendClientMessage(playerid, 0x00FF00FF, "Waktu tunggu 3 menit selesai! Sekarang Anda bisa menggunakan /acceptdeath.");

    // Reset death timer
    PlayerInfo[playerid][pDeathTimer] = 0;

    return 1;
}

// Function to revive player
stock RevivePlayer(playerid)
{
    if(!PlayerInfo[playerid][pIsDead])
        return 0;

    // Stop death timer if still running
    if(PlayerInfo[playerid][pDeathTimer] != 0)
    {
        KillTimer(PlayerInfo[playerid][pDeathTimer]);
        PlayerInfo[playerid][pDeathTimer] = 0;
    }

    // Stop shake timer if still running
    if(PlayerInfo[playerid][pShakeTimer] != 0)
    {
        KillTimer(PlayerInfo[playerid][pShakeTimer]);
        PlayerInfo[playerid][pShakeTimer] = 0;
    }

    // Stop animation timer if still running
    if(PlayerInfo[playerid][pAnimTimer] != 0)
    {
        KillTimer(PlayerInfo[playerid][pAnimTimer]);
        PlayerInfo[playerid][pAnimTimer] = 0;
    }



    // Clear death animation and reset camera
    ClearAnimations(playerid);
    SetCameraBehindPlayer(playerid);
    SetPlayerDrunkLevel(playerid, 0); // Reset drunk effect

    // Reset death status
    PlayerInfo[playerid][pIsDead] = false;

    // Deduct money ($5000)
    new currentMoney = GetPlayerMoney(playerid);
    new newMoney = currentMoney - 5000;
    if(newMoney < 0) newMoney = 0;

    ResetPlayerMoney(playerid);
    GivePlayerMoney(playerid, newMoney);
    PlayerInfo[playerid][pMoney] = newMoney;

    // Set health to 50% (armour remains unchanged)
    new Float:newHealth = PlayerInfo[playerid][pMaxHealth] * 0.5;

    PlayerInfo[playerid][pHealth] = newHealth;
    SetPlayerHealth(playerid, newHealth);

    // Spawn at hospital
    SetPlayerPos(playerid, 1177.4576, -1323.6398, 14.0717);
    SetPlayerFacingAngle(playerid, 269.5341);

    // Update position in data
    PlayerInfo[playerid][pPosX] = 1177.4576;
    PlayerInfo[playerid][pPosY] = -1323.6398;
    PlayerInfo[playerid][pPosZ] = 14.0717;
    PlayerInfo[playerid][pAngle] = 269.5341;

    // Unfreeze player
    TogglePlayerControllable(playerid, true);

    // Reset camera
    SetCameraBehindPlayer(playerid);

    // Save data
    SavePlayerData(playerid);

    // Send revival messages
    SendClientMessage(playerid, 0x00FF00FF, "Anda telah hidup kembali di rumah sakit!");
    SendClientMessage(playerid, 0xFFFF00FF, "Biaya pengobatan: $50.00");

    return 1;
}

// /acceptdeath command - Accept death and revive
CMD:acceptdeath(playerid, params[])
{
    if(!PlayerInfo[playerid][pLogged])
        return SendClientMessage(playerid, 0xFF0000FF, "ERROR: Anda belum login!");

    if(!PlayerInfo[playerid][pIsDead])
        return SendClientMessage(playerid, 0xFF0000FF, "ERROR: Anda tidak sedang mati!");

    // Check if 3-minute timer is still running (player must wait)
    if(PlayerInfo[playerid][pDeathTimer] != 0)
        return SendClientMessage(playerid, 0xFF0000FF, "ERROR: Anda harus menunggu 3 menit sebelum bisa menggunakan /acceptdeath!");

    // Stop any remaining timers
    if(PlayerInfo[playerid][pShakeTimer] != 0)
    {
        KillTimer(PlayerInfo[playerid][pShakeTimer]);
        PlayerInfo[playerid][pShakeTimer] = 0;
    }

    if(PlayerInfo[playerid][pAnimTimer] != 0)
    {
        KillTimer(PlayerInfo[playerid][pAnimTimer]);
        PlayerInfo[playerid][pAnimTimer] = 0;
    }

    // Reset death status
    PlayerInfo[playerid][pIsDead] = false;

    // Deduct money ($50.00)
    new currentMoney = GetPlayerMoney(playerid);
    new newMoney = currentMoney - 5000;
    if(newMoney < 0) newMoney = 0;

    ResetPlayerMoney(playerid);
    GivePlayerMoney(playerid, newMoney);
    PlayerInfo[playerid][pMoney] = newMoney;

    // Set max health to 50 and current health to 50
    PlayerInfo[playerid][pMaxHealth] = 50.0;
    PlayerInfo[playerid][pHealth] = 50.0;
    SetPlayerHealth(playerid, 50.0);

    // Reset drunk level
    SetPlayerDrunkLevel(playerid, 0);

    // Hide red screen
    TextDrawHideForPlayer(playerid, RedScreen);

    // Clear animations
    ClearAnimations(playerid);
    SetCameraBehindPlayer(playerid);

    // Teleport to hospital coordinates (1177.4576,-1323.6398,14.0717,269.5341)
    SetPlayerPos(playerid, 1177.4576, -1323.6398, 14.0717);
    SetPlayerFacingAngle(playerid, 269.5341);

    // Update position data
    PlayerInfo[playerid][pPosX] = 1177.4576;
    PlayerInfo[playerid][pPosY] = -1323.6398;
    PlayerInfo[playerid][pPosZ] = 14.0717;
    PlayerInfo[playerid][pAngle] = 269.5341;

    // Unfreeze player
    TogglePlayerControllable(playerid, true);

    // Save death status to database (now alive)
    SavePlayerDeathStatus(playerid);
    SavePlayerData(playerid);

    // Send messages
    SendClientMessage(playerid, COLOR_KAKZAH, "HOSPITAL: {FFFFFF}Anda telah dihidupkan di rumah sakit!");
    SendClientMessage(playerid, COLOR_KAKZAH, "BIAYA: {FFFFFF}Anda telah di kenakan biaya sebesar {00FF00}$50.00.");
    
    return 1;
}

// /aheal command - Admin heal/revive dead player (Admin Level 2+)
CMD:aheal(playerid, params[])
{
    if(!PlayerInfo[playerid][pLogged])
        return SendClientMessage(playerid, 0xFF0000FF, "ERROR: Anda belum login!");

    if(PlayerInfo[playerid][pAdminLevel] < 2)
        return SendClientMessage(playerid, 0xFF0000FF, "ERROR: Hanya admin level 2+ yang bisa menggunakan command ini!");

    new targetid;
    if(sscanf(params, "u", targetid))
        return SendClientMessage(playerid, 0x808080FF, "USAGE: /aheal [playerid]");

    if(!IsPlayerConnected(targetid) || !PlayerInfo[targetid][pLogged])
        return SendClientMessage(playerid, 0xFF0000FF, "ERROR: Player tidak online atau belum login!");

    new targetName[MAX_PLAYER_NAME], adminName[32];
    GetPlayerName(targetid, targetName, sizeof(targetName));
    GetPlayerAdminName(playerid, adminName, sizeof(adminName));

    // Check if target is dead
    if(!PlayerInfo[targetid][pIsDead])
    {
        // If not dead, just heal to max health (no armour change)
        SetPlayerHealthSafe(targetid, PlayerInfo[targetid][pMaxHealth]);

        // Save to database
        SavePlayerData(targetid);

        // Log admin command
        new logInfo[64];
        format(logInfo, sizeof(logInfo), "Healed to max HP");
        LogAdminCommand(playerid, "/aheal", targetName, logInfo);

        // Send messages
        new message[128];
        format(message, sizeof(message), "Admin %s telah mengheal HP Anda ke maksimal.", adminName);
        SendClientMessage(targetid, 0x00FF00FF, message);

        format(message, sizeof(message), "Anda telah mengheal HP %s ke maksimal.", targetName);
        SendClientMessage(playerid, 0x00FF00FF, message);

        return 1;
    }

    // Target is dead - revive without penalties
    AdminRevivePlayer(targetid);

    // Log admin command
    LogAdminCommand(playerid, "/aheal", targetName, "Revived from death (max HP, no penalties)");

    // Send messages
    new message[128];
    format(message, sizeof(message), "Admin %s telah menghidupkan Anda tanpa penalty!", adminName);
    SendClientMessage(targetid, 0x00FF00FF, message);

    format(message, sizeof(message), "Anda telah menghidupkan %s dari kematian.", targetName);
    SendClientMessage(playerid, 0x00FF00FF, message);

    return 1;
}

// /gethere command (Admin Level 2 only)
CMD:gethere(playerid, params[])
{
    if(!PlayerInfo[playerid][pLogged]) return SendClientMessage(playerid, 0xFF0000FF, "ERROR: Anda belum login!");

    if(PlayerInfo[playerid][pAdminLevel] < 2)
        return SendClientMessage(playerid, 0xFF0000FF, "ERROR: Hanya admin level 2+ yang bisa menggunakan command ini!");

    new targetid;
    if(sscanf(params, "u", targetid))
        return SendClientMessage(playerid, 0x808080FF, "USAGE: /gethere [playerid]");

    if(!IsPlayerConnected(targetid) || !PlayerInfo[targetid][pLogged])
        return SendClientMessage(playerid, 0xFF0000FF, "ERROR: Player tidak online atau belum login!");

    if(targetid == playerid)
        return SendClientMessage(playerid, 0xFF0000FF, "ERROR: Anda tidak bisa teleport diri sendiri!");

    // Get admin position
    new Float:x, Float:y, Float:z, Float:angle;
    GetPlayerPos(playerid, x, y, z);
    GetPlayerFacingAngle(playerid, angle);

    // Get admin's interior and virtual world
    new interior = GetPlayerInterior(playerid);
    new virtualworld = GetPlayerVirtualWorld(playerid);

    // Teleport target to admin
    SetPlayerPos(targetid, x + 2.0, y, z); // Offset by 2 units so they don't spawn inside admin
    SetPlayerFacingAngle(targetid, angle);
    SetPlayerInterior(targetid, interior);
    SetPlayerVirtualWorld(targetid, virtualworld);

    // Update target's saved position
    PlayerInfo[targetid][pPosX] = x + 2.0;
    PlayerInfo[targetid][pPosY] = y;
    PlayerInfo[targetid][pPosZ] = z;
    PlayerInfo[targetid][pAngle] = angle;
    PlayerInfo[targetid][pInterior] = interior;
    PlayerInfo[targetid][pVirtualWorld] = virtualworld;

    // Log admin command
    new targetName[MAX_PLAYER_NAME], adminName[MAX_PLAYER_NAME];
    GetPlayerName(targetid, targetName, sizeof(targetName));
    GetPlayerName(playerid, adminName, sizeof(adminName));
    LogAdminCommand(playerid, "/gethere", targetName, "Player teleported to admin");

    new adminMsg[128], targetMsg[128];
    format(adminMsg, sizeof(adminMsg), "ADMIN: You teleported %s to your location.", targetName);
    format(targetMsg, sizeof(targetMsg), "TELEPORTED: Admin %s teleported you to their location.", adminName);

    SendClientMessage(playerid, COLOR_ADMIN, adminMsg);
    SendClientMessage(targetid, COLOR_ADMIN, targetMsg);

    return 1;
}

// /giveweapon command (Admin Level 5+) - Give custom weapon with ammo
CMD:giveweapon(playerid, params[])
{
    if(!PlayerInfo[playerid][pLogged]) return SendClientMessage(playerid, 0xFF0000FF, "ERROR: Anda belum login!");

    if(PlayerInfo[playerid][pAdminLevel] < 5)
        return SendClientMessage(playerid, 0xFF0000FF, "ERROR: Hanya admin level 5+ yang bisa menggunakan command ini!");

    new targetid, weaponid, ammo;
    if(sscanf(params, "udd", targetid, weaponid, ammo))
        return SendClientMessage(playerid, 0x808080FF, "USAGE: /giveweapon [playerid] [weaponid] [ammo]");

    if(!IsPlayerConnected(targetid) || !PlayerInfo[targetid][pLogged])
        return SendClientMessage(playerid, 0xFF0000FF, "ERROR: Player tidak online atau belum login!");

    if(weaponid < 1 || weaponid > 46)
        return SendClientMessage(playerid, 0xFF0000FF, "ERROR: Weapon ID tidak valid! (1-46)");

    if(ammo < 1 || ammo > 9999)
        return SendClientMessage(playerid, 0xFF0000FF, "ERROR: Ammo tidak valid! (1-9999)");

    // Give custom weapon
    GivePlayerCustomWeapon(targetid, weaponid, ammo);

    // Get weapon name
    new weaponName[32];
    GetWeaponName(weaponid, weaponName, sizeof(weaponName));

    // Log admin command
    new targetName[MAX_PLAYER_NAME], adminName[MAX_PLAYER_NAME];
    GetPlayerName(targetid, targetName, sizeof(targetName));
    GetPlayerName(playerid, adminName, sizeof(adminName));
    LogAdminCommand(playerid, "/giveweapon", targetName, "Custom weapon given");

    new adminMsg[128], targetMsg[128];
    format(adminMsg, sizeof(adminMsg), "ADMIN: {FFFFFF}You gave %s a %s with %d ammo.", targetName, weaponName, ammo);
    format(targetMsg, sizeof(targetMsg), "WEAPON: {FFFFFF}Admin %s gave you a %s with %d ammo.", adminName, weaponName, ammo);

    SendClientMessage(playerid, 0x00FF00FF, adminMsg);
    SendClientMessage(targetid, 0x00FF00FF, targetMsg);

    return 1;
}

// /reload command - Reload current weapon
CMD:reload(playerid, params[])
{
    if(!PlayerInfo[playerid][pLogged])
        return SendClientMessage(playerid, 0xFF0000FF, "ERROR: Anda belum login!");

    ReloadWeapon(playerid);
    return 1;
}

// /ammo command - Check current ammo
CMD:ammo(playerid, params[])
{
    if(!PlayerInfo[playerid][pLogged])
        return SendClientMessage(playerid, 0xFF0000FF, "ERROR: Anda belum login!");

    new weaponid = GetPlayerWeapon(playerid);
    if(weaponid == 0)
        return SendClientMessage(playerid, 0xFF8000FF, "WEAPON: {FFFFFF}You are not holding any weapon.");

    new slot = GetWeaponSlot(weaponid);
    if(slot == -1 || WeaponMagazineSize[weaponid] == 0)
        return SendClientMessage(playerid, 0xFF8000FF, "WEAPON: {FFFFFF}This weapon doesn't use ammo.");

    new weaponName[32];
    GetWeaponName(weaponid, weaponName, sizeof(weaponName));

    new ammoMsg[128];
    format(ammoMsg, sizeof(ammoMsg), "AMMO: {FFFFFF}%s - {FFFF00}%d{FFFFFF}/{FFFF00}%d{FFFFFF} rounds",
        weaponName, PlayerInfo[playerid][pWeaponAmmo][slot], PlayerInfo[playerid][pWeaponMagazine][slot]);
    SendClientMessage(playerid, 0x00FF00FF, ammoMsg);

    return 1;
}

// /items command - Show inventory
CMD:items(playerid, params[])
{
    if(!PlayerInfo[playerid][pLogged])
        return SendClientMessage(playerid, 0xFF0000FF, "ERROR: Anda belum login!");

    ShowInventoryDialog(playerid);
    return 1;
}

// Function to show inventory dialog
stock ShowInventoryDialog(playerid)
{
    new dialogStr[2048];
    new tempStr[256];
    new itemCount = 0;

    // Header
    strcat(dialogStr, "Items\tAmount\n");

    // Money (always first)
    PlayerInfo[playerid][pInventoryMoney] = PlayerInfo[playerid][pMoney]; // Sync money
    new moneyStr[32];
    FormatInventoryMoney(PlayerInfo[playerid][pInventoryMoney], moneyStr, sizeof(moneyStr));
    format(tempStr, sizeof(tempStr), "Money\t\t\t{2C6836}$%s\n", moneyStr);
    strcat(dialogStr, tempStr);
    itemCount++;

    // Weapons and ammo
    for(new i = 0; i < 13; i++)
    {
        if(PlayerInfo[playerid][pInventoryWeapons][i] != 0)
        {
            new weaponName[32];
            GetWeaponName(PlayerInfo[playerid][pInventoryWeapons][i], weaponName, sizeof(weaponName));

            // Weapon color (red if illegal, white if legal)
            new weaponColor[10] = "{FFFFFF}";
            if(IsWeaponIllegal(PlayerInfo[playerid][pInventoryWeapons][i]))
                strcpy(weaponColor, "{FF0000}", 10);

            // Show weapon with current ammo (or "Empty" if 0)
            if(PlayerInfo[playerid][pWeaponAmmo][i] > 0)
            {
                format(tempStr, sizeof(tempStr), "%s[WEAPON: %s]\t\t%s%d\n",
                    weaponColor, weaponName, weaponColor, PlayerInfo[playerid][pWeaponAmmo][i]);
            }
            else
            {
                format(tempStr, sizeof(tempStr), "%s[WEAPON: %s]\t\t%sEmpty\n",
                    weaponColor, weaponName, weaponColor);
            }
            strcat(dialogStr, tempStr);
            itemCount++;

            // Show ammo units if available
            if(PlayerInfo[playerid][pInventoryAmmoUnits][i] > 0)
            {
                format(tempStr, sizeof(tempStr), "%s[AMMO: %s]\t\t%s%d\n",
                    weaponColor, weaponName, weaponColor, PlayerInfo[playerid][pInventoryAmmoUnits][i]);
                strcat(dialogStr, tempStr);
                itemCount++;
            }
        }
    }

    Dialog_Show(playerid, InventoryMain, DIALOG_STYLE_TABLIST_HEADERS, "Inventory", dialogStr, "Select", "Close");
    return 1;
}

// Function to format money for inventory display
stock FormatInventoryMoney(money, output[], maxlength)
{
    new dollars = money / 100;
    new cents = money % 100;
    format(output, maxlength, "%d.%02d", dollars, cents);
    return 1;
}

// Function to show weapon action dialog
stock ShowWeaponActionDialog(playerid, weaponSlot)
{
    if(weaponSlot < 0 || weaponSlot >= 13)
        return 0;

    new weaponid = PlayerInfo[playerid][pInventoryWeapons][weaponSlot];
    if(weaponid == 0)
        return 0;

    // Store selected weapon slot
    PlayerInfo[playerid][pSelectedWeaponSlot] = weaponSlot;

    new weaponName[32];
    GetWeaponName(weaponid, weaponName, sizeof(weaponName));

    new dialogTitle[64];
    format(dialogTitle, sizeof(dialogTitle), "Weapon: %s", weaponName);

    new dialogStr[512];
    new ammoStatus[32];
    if(PlayerInfo[playerid][pWeaponAmmo][weaponSlot] > 0)
        format(ammoStatus, sizeof(ammoStatus), "{FFFF00}%d", PlayerInfo[playerid][pWeaponAmmo][weaponSlot]);
    else
        strcpy(ammoStatus, "{FF0000}Empty", 32);

    format(dialogStr, sizeof(dialogStr),
        "{FFFFFF}Current Ammo: %s{FFFFFF} rounds\n\
        {FFFFFF}Magazine Size: {FFFF00}%d{FFFFFF} rounds\n\
        {FFFFFF}Ammo Units Available: {FFFF00}%d{FFFFFF} units\n\
        {808080}(1 unit = 100 ammo, refills magazine)\n\n\
        {FFFFFF}Select an action:",
        ammoStatus,
        PlayerInfo[playerid][pWeaponMagazine][weaponSlot],
        PlayerInfo[playerid][pInventoryAmmoUnits][weaponSlot]);

    new buttonText[64];
    if(PlayerInfo[playerid][pInventoryAmmoUnits][weaponSlot] > 0)
        strcpy(buttonText, "Reload Ammo", 64);
    else
        strcpy(buttonText, "No Ammo Units", 64);

    Dialog_Show(playerid, WeaponAction, DIALOG_STYLE_LIST, dialogTitle,
        buttonText, "Select", "Back");

    return 1;
}

// /checkinventory command (Admin Level 2+) - Check player's inventory
CMD:checkinventory(playerid, params[])
{
    if(!PlayerInfo[playerid][pLogged]) return SendClientMessage(playerid, 0xFF0000FF, "ERROR: Anda belum login!");

    if(PlayerInfo[playerid][pAdminLevel] < 2)
        return SendClientMessage(playerid, 0xFF0000FF, "ERROR: Hanya admin level 2+ yang bisa menggunakan command ini!");

    new targetid;
    if(sscanf(params, "u", targetid))
        return SendClientMessage(playerid, 0x808080FF, "USAGE: /checkinventory [playerid]");

    if(!IsPlayerConnected(targetid) || !PlayerInfo[targetid][pLogged])
        return SendClientMessage(playerid, 0xFF0000FF, "ERROR: Player tidak online atau belum login!");

    // Show target's inventory to admin
    ShowAdminInventoryDialog(playerid, targetid);

    // Log admin command
    new targetName[MAX_PLAYER_NAME], adminName[MAX_PLAYER_NAME];
    GetPlayerName(targetid, targetName, sizeof(targetName));
    GetPlayerName(playerid, adminName, sizeof(adminName));
    LogAdminCommand(playerid, "/checkinventory", targetName, "Inventory checked");

    return 1;
}

// /removeinventory command (Admin Level 5+) - Remove all items from player's inventory
CMD:removeinventory(playerid, params[])
{
    if(!PlayerInfo[playerid][pLogged]) return SendClientMessage(playerid, 0xFF0000FF, "ERROR: Anda belum login!");

    if(PlayerInfo[playerid][pAdminLevel] < 5)
        return SendClientMessage(playerid, 0xFF0000FF, "ERROR: Hanya admin level 5+ yang bisa menggunakan command ini!");

    new targetid;
    if(sscanf(params, "u", targetid))
        return SendClientMessage(playerid, 0x808080FF, "USAGE: /removeinventory [playerid]");

    if(!IsPlayerConnected(targetid) || !PlayerInfo[targetid][pLogged])
        return SendClientMessage(playerid, 0xFF0000FF, "ERROR: Player tidak online atau belum login!");

    // Clear all weapons and ammo from inventory
    for(new i = 0; i < 13; i++)
    {
        PlayerInfo[targetid][pInventoryWeapons][i] = 0;
        PlayerInfo[targetid][pInventoryAmmoUnits][i] = 0;
        PlayerInfo[targetid][pWeaponAmmo][i] = 0;
        PlayerInfo[targetid][pWeaponMagazine][i] = 0;
    }

    // Remove all weapons from player
    ResetPlayerWeapons(targetid);

    // Hide ammo display
    if(PlayerInfo[targetid][pAmmoTDShown])
    {
        TextDrawHideForPlayer(targetid, PlayerInfo[targetid][pAmmoTD]);
        PlayerInfo[targetid][pAmmoTDShown] = false;
    }

    // Log admin command
    new targetName[MAX_PLAYER_NAME], adminName[MAX_PLAYER_NAME];
    GetPlayerName(targetid, targetName, sizeof(targetName));
    GetPlayerName(playerid, adminName, sizeof(adminName));
    LogAdminCommand(playerid, "/removeinventory", targetName, "Inventory cleared");

    new adminMsg[128], targetMsg[128];
    format(adminMsg, sizeof(adminMsg), "ADMIN: {FFFFFF}You cleared %s's inventory.", targetName);
    format(targetMsg, sizeof(targetMsg), "INVENTORY: {FFFFFF}Admin %s cleared your inventory.", adminName);

    SendClientMessage(playerid, 0x00FF00FF, adminMsg);
    SendClientMessage(targetid, 0xFF8000FF, targetMsg);

    return 1;
}

// Function to show admin inventory dialog (for checking other player's inventory)
stock ShowAdminInventoryDialog(playerid, targetid)
{
    new dialogStr[2048];
    new tempStr[256];
    new itemCount = 0;

    new targetName[MAX_PLAYER_NAME];
    GetPlayerName(targetid, targetName, sizeof(targetName));

    // Header
    format(tempStr, sizeof(tempStr), "{FFFFFF}%s's Inventory\n", targetName);
    strcat(dialogStr, tempStr);
    strcat(dialogStr, "{FFFF00}Items\t\t\tAmount\n");

    // Money (always first)
    PlayerInfo[targetid][pInventoryMoney] = PlayerInfo[targetid][pMoney]; // Sync money
    new moneyStr[32];
    FormatInventoryMoney(PlayerInfo[targetid][pInventoryMoney], moneyStr, sizeof(moneyStr));
    format(tempStr, sizeof(tempStr), "{00FF00}Money\t\t\t{00FF00}$%s\n", moneyStr);
    strcat(dialogStr, tempStr);
    itemCount++;

    // Weapons and ammo
    for(new i = 0; i < 13; i++)
    {
        if(PlayerInfo[targetid][pInventoryWeapons][i] != 0)
        {
            new weaponName[32];
            GetWeaponName(PlayerInfo[targetid][pInventoryWeapons][i], weaponName, sizeof(weaponName));

            // Weapon color (red if illegal, white if legal)
            new weaponColor[10] = "{FFFFFF}";
            if(IsWeaponIllegal(PlayerInfo[targetid][pInventoryWeapons][i]))
                strcpy(weaponColor, "{FF0000}", 10);

            // Show weapon with current ammo (or "Empty" if 0)
            if(PlayerInfo[targetid][pWeaponAmmo][i] > 0)
            {
                format(tempStr, sizeof(tempStr), "%s[WEAPON: %s]\t\t%s%d\n",
                    weaponColor, weaponName, weaponColor, PlayerInfo[targetid][pWeaponAmmo][i]);
            }
            else
            {
                format(tempStr, sizeof(tempStr), "%s[WEAPON: %s]\t\t%sEmpty\n",
                    weaponColor, weaponName, weaponColor);
            }
            strcat(dialogStr, tempStr);
            itemCount++;

            // Show ammo units if available
            if(PlayerInfo[targetid][pInventoryAmmoUnits][i] > 0)
            {
                format(tempStr, sizeof(tempStr), "%s[AMMO: %s]\t\t%s%d\n",
                    weaponColor, weaponName, weaponColor, PlayerInfo[targetid][pInventoryAmmoUnits][i]);
                strcat(dialogStr, tempStr);
                itemCount++;
            }
        }
    }

    new dialogTitle[64];
    format(dialogTitle, sizeof(dialogTitle), "%s's Inventory", targetName);
    Dialog_Show(playerid, AdminInventory, DIALOG_STYLE_TABLIST_HEADERS, dialogTitle, dialogStr, "Close", "");
    return 1;
}

// Function to revive player by admin (no penalties)
stock AdminRevivePlayer(playerid)
{
    if(!PlayerInfo[playerid][pIsDead])
        return 0;

    // Stop death timer if still running
    if(PlayerInfo[playerid][pDeathTimer] != 0)
    {
        KillTimer(PlayerInfo[playerid][pDeathTimer]);
        PlayerInfo[playerid][pDeathTimer] = 0;
    }

    // Reset death status
    PlayerInfo[playerid][pIsDead] = false;

    // NO money deduction (admin heal is free)

    // Set health to max (armour remains unchanged)
    PlayerInfo[playerid][pHealth] = PlayerInfo[playerid][pMaxHealth];
    SetPlayerHealth(playerid, PlayerInfo[playerid][pMaxHealth]);

    // Hidup di tempat mati (tidak di hospital)
    SetPlayerPos(playerid, PlayerInfo[playerid][pDeathX], PlayerInfo[playerid][pDeathY], PlayerInfo[playerid][pDeathZ]);
    SetPlayerFacingAngle(playerid, PlayerInfo[playerid][pDeathAngle]);

    // Update position in data ke lokasi kematian
    PlayerInfo[playerid][pPosX] = PlayerInfo[playerid][pDeathX];
    PlayerInfo[playerid][pPosY] = PlayerInfo[playerid][pDeathY];
    PlayerInfo[playerid][pPosZ] = PlayerInfo[playerid][pDeathZ];
    PlayerInfo[playerid][pAngle] = PlayerInfo[playerid][pDeathAngle];

    // Clear death animation and unfreeze player
    ClearAnimations(playerid);
    TogglePlayerControllable(playerid, true);

    // Hide red screen and reset camera and drunk effect
    TextDrawHideForPlayer(playerid, RedScreen);
    SetCameraBehindPlayer(playerid);
    SetPlayerDrunkLevel(playerid, 0);

    // Save death status to database (now alive)
    SavePlayerDeathStatus(playerid);
    SavePlayerData(playerid);

    // Send revival message
    SendClientMessage(playerid, 0x00FF00FF, "Anda telah dihidupkan oleh admin dengan HP maksimal di lokasi kematian!");

    return 1;
}

// ========== JAIL SYSTEM FUNCTIONS ==========

// Forward declarations for jail system
forward OnJailTimerUpdate(playerid);
forward OnJailTimeEnd(playerid);
forward PutPlayerBackInJail(playerid);

// Function to jail a player
stock JailPlayer(playerid, minutes, const reason[], const adminName[])
{
    if(PlayerInfo[playerid][pJailed])
        return 0; // Already jailed

    // Set jail data
    PlayerInfo[playerid][pJailed] = true;
    PlayerInfo[playerid][pJailTime] = minutes * 60; // Convert to seconds

    // Get random jail position
    new jailSlot = random(sizeof(JailPositions));

    // Set player position and virtual world
    SetPlayerPos(playerid, JailPositions[jailSlot][0], JailPositions[jailSlot][1], JailPositions[jailSlot][2]);
    SetPlayerFacingAngle(playerid, JailPositions[jailSlot][3]);
    SetPlayerVirtualWorld(playerid, 1000);
    SetPlayerInterior(playerid, 0);

    // Update player data
    PlayerInfo[playerid][pVirtualWorld] = 1000;
    PlayerInfo[playerid][pInterior] = 0;

    // Create jail textdraw
    CreateJailTextDraw(playerid);

    // Start jail timer (updates every second)
    new funcname[] = "OnJailTimerUpdate";
    new formatstr[] = "i";
    PlayerInfo[playerid][pJailTimer] = SetTimerEx(funcname, 1000, true, formatstr, playerid);

    // Add to warns database
    new targetName[MAX_PLAYER_NAME];
    GetPlayerName(playerid, targetName, sizeof(targetName));
    AddPlayerWarn(targetName, "JAIL", adminName, reason);

    // Save player data
    SavePlayerData(playerid);

    return 1;
}

// Function to unjail a player
stock UnjailPlayer(playerid)
{
    if(!PlayerInfo[playerid][pJailed])
        return 0; // Not jailed

    // Reset jail data
    PlayerInfo[playerid][pJailed] = false;
    PlayerInfo[playerid][pJailTime] = 0;

    // Kill jail timer
    if(PlayerInfo[playerid][pJailTimer] != 0)
    {
        KillTimer(PlayerInfo[playerid][pJailTimer]);
        PlayerInfo[playerid][pJailTimer] = 0;
    }

    // Destroy jail textdraw
    DestroyJailTextDraw(playerid);

    // Set player back to normal world
    SetPlayerVirtualWorld(playerid, 0);
    SetPlayerInterior(playerid, 0);

    // Update player data
    PlayerInfo[playerid][pVirtualWorld] = 0;
    PlayerInfo[playerid][pInterior] = 0;

    // Teleport to release location
    SetPlayerPos(playerid, 1548.3636, -1675.5205, 14.4908);
    SetPlayerFacingAngle(playerid, 88.6995);

    // Save player data
    SavePlayerData(playerid);

    SendClientMessage(playerid, 0x00FF00FF, "Anda telah dibebaskan dari penjara OOC!");

    return 1;
}

// Function to create jail textdraw
stock CreateJailTextDraw(playerid)
{
    // Create jail textdraw 0 - "Jail Time:" label
    PlayerInfo[playerid][pJailTextDraw][0] = CreatePlayerTextDraw(playerid, 308.000000, 383.000000, "Jail Time:");
    PlayerTextDrawFont(playerid, PlayerInfo[playerid][pJailTextDraw][0], 1);
    PlayerTextDrawLetterSize(playerid, PlayerInfo[playerid][pJailTextDraw][0], 0.266665, 1.549998);
    PlayerTextDrawTextSize(playerid, PlayerInfo[playerid][pJailTextDraw][0], 400.000000, 17.000000);
    PlayerTextDrawSetOutline(playerid, PlayerInfo[playerid][pJailTextDraw][0], 1);
    PlayerTextDrawSetShadow(playerid, PlayerInfo[playerid][pJailTextDraw][0], 0);
    PlayerTextDrawAlignment(playerid, PlayerInfo[playerid][pJailTextDraw][0], 3);
    PlayerTextDrawColor(playerid, PlayerInfo[playerid][pJailTextDraw][0], -16776961);
    PlayerTextDrawBackgroundColor(playerid, PlayerInfo[playerid][pJailTextDraw][0], 255);
    PlayerTextDrawBoxColor(playerid, PlayerInfo[playerid][pJailTextDraw][0], 50);
    PlayerTextDrawUseBox(playerid, PlayerInfo[playerid][pJailTextDraw][0], 0);
    PlayerTextDrawSetProportional(playerid, PlayerInfo[playerid][pJailTextDraw][0], 1);
    PlayerTextDrawSetSelectable(playerid, PlayerInfo[playerid][pJailTextDraw][0], 0);

    // Create jail textdraw 1 - Background box
    PlayerInfo[playerid][pJailTextDraw][1] = CreatePlayerTextDraw(playerid, 231.000000, 384.000000, "_");
    PlayerTextDrawFont(playerid, PlayerInfo[playerid][pJailTextDraw][1], 1);
    PlayerTextDrawLetterSize(playerid, PlayerInfo[playerid][pJailTextDraw][1], 0.600000, 1.549998);
    PlayerTextDrawTextSize(playerid, PlayerInfo[playerid][pJailTextDraw][1], 400.000000, 15.000000);
    PlayerTextDrawSetOutline(playerid, PlayerInfo[playerid][pJailTextDraw][1], 1);
    PlayerTextDrawSetShadow(playerid, PlayerInfo[playerid][pJailTextDraw][1], 0);
    PlayerTextDrawAlignment(playerid, PlayerInfo[playerid][pJailTextDraw][1], 1);
    PlayerTextDrawColor(playerid, PlayerInfo[playerid][pJailTextDraw][1], -1);
    PlayerTextDrawBackgroundColor(playerid, PlayerInfo[playerid][pJailTextDraw][1], 255);
    PlayerTextDrawBoxColor(playerid, PlayerInfo[playerid][pJailTextDraw][1], 175);
    PlayerTextDrawUseBox(playerid, PlayerInfo[playerid][pJailTextDraw][1], 1);
    PlayerTextDrawSetProportional(playerid, PlayerInfo[playerid][pJailTextDraw][1], 1);
    PlayerTextDrawSetSelectable(playerid, PlayerInfo[playerid][pJailTextDraw][1], 0);

    // Create jail textdraw 2 - Top border
    PlayerInfo[playerid][pJailTextDraw][2] = CreatePlayerTextDraw(playerid, 231.000000, 380.000000, "_");
    PlayerTextDrawFont(playerid, PlayerInfo[playerid][pJailTextDraw][2], 1);
    PlayerTextDrawLetterSize(playerid, PlayerInfo[playerid][pJailTextDraw][2], 0.600000, -0.049998);
    PlayerTextDrawTextSize(playerid, PlayerInfo[playerid][pJailTextDraw][2], 400.000000, 15.000000);
    PlayerTextDrawSetOutline(playerid, PlayerInfo[playerid][pJailTextDraw][2], 1);
    PlayerTextDrawSetShadow(playerid, PlayerInfo[playerid][pJailTextDraw][2], 0);
    PlayerTextDrawAlignment(playerid, PlayerInfo[playerid][pJailTextDraw][2], 1);
    PlayerTextDrawColor(playerid, PlayerInfo[playerid][pJailTextDraw][2], -1);
    PlayerTextDrawBackgroundColor(playerid, PlayerInfo[playerid][pJailTextDraw][2], 255);
    PlayerTextDrawBoxColor(playerid, PlayerInfo[playerid][pJailTextDraw][2], 225);
    PlayerTextDrawUseBox(playerid, PlayerInfo[playerid][pJailTextDraw][2], 1);
    PlayerTextDrawSetProportional(playerid, PlayerInfo[playerid][pJailTextDraw][2], 1);
    PlayerTextDrawSetSelectable(playerid, PlayerInfo[playerid][pJailTextDraw][2], 0);

    // Create jail textdraw 3 - Time display
    new timeString[32];
    format(timeString, sizeof(timeString), "%d Second", PlayerInfo[playerid][pJailTime]);
    PlayerInfo[playerid][pJailTextDraw][3] = CreatePlayerTextDraw(playerid, 312.000000, 383.000000, timeString);
    PlayerTextDrawFont(playerid, PlayerInfo[playerid][pJailTextDraw][3], 1);
    PlayerTextDrawLetterSize(playerid, PlayerInfo[playerid][pJailTextDraw][3], 0.229166, 1.500000);
    PlayerTextDrawTextSize(playerid, PlayerInfo[playerid][pJailTextDraw][3], 400.000000, 17.000000);
    PlayerTextDrawSetOutline(playerid, PlayerInfo[playerid][pJailTextDraw][3], 1);
    PlayerTextDrawSetShadow(playerid, PlayerInfo[playerid][pJailTextDraw][3], 0);
    PlayerTextDrawAlignment(playerid, PlayerInfo[playerid][pJailTextDraw][3], 1);
    PlayerTextDrawColor(playerid, PlayerInfo[playerid][pJailTextDraw][3], -1);
    PlayerTextDrawBackgroundColor(playerid, PlayerInfo[playerid][pJailTextDraw][3], 255);
    PlayerTextDrawBoxColor(playerid, PlayerInfo[playerid][pJailTextDraw][3], 50);
    PlayerTextDrawUseBox(playerid, PlayerInfo[playerid][pJailTextDraw][3], 0);
    PlayerTextDrawSetProportional(playerid, PlayerInfo[playerid][pJailTextDraw][3], 1);
    PlayerTextDrawSetSelectable(playerid, PlayerInfo[playerid][pJailTextDraw][3], 0);

    // Show all textdraws
    for(new i = 0; i < 4; i++)
    {
        PlayerTextDrawShow(playerid, PlayerInfo[playerid][pJailTextDraw][i]);
    }
}

// Function to destroy jail textdraw
stock DestroyJailTextDraw(playerid)
{
    for(new i = 0; i < 4; i++)
    {
        if(PlayerInfo[playerid][pJailTextDraw][i] != PlayerText:INVALID_TEXT_DRAW)
        {
            PlayerTextDrawHide(playerid, PlayerInfo[playerid][pJailTextDraw][i]);
            PlayerTextDrawDestroy(playerid, PlayerInfo[playerid][pJailTextDraw][i]);
            PlayerInfo[playerid][pJailTextDraw][i] = PlayerText:INVALID_TEXT_DRAW;
        }
    }
}

// Function to update jail textdraw
stock UpdateJailTextDraw(playerid)
{
    if(PlayerInfo[playerid][pJailed] && PlayerInfo[playerid][pJailTextDraw][3] != PlayerText:INVALID_TEXT_DRAW)
    {
        new string[32];
        format(string, sizeof(string), "%d Second", PlayerInfo[playerid][pJailTime]);
        PlayerTextDrawSetString(playerid, PlayerInfo[playerid][pJailTextDraw][3], string);
    }
}

// Timer callback for jail countdown
public OnJailTimerUpdate(playerid)
{
    if(!IsPlayerConnected(playerid) || !PlayerInfo[playerid][pJailed])
    {
        if(PlayerInfo[playerid][pJailTimer] != 0)
        {
            KillTimer(PlayerInfo[playerid][pJailTimer]);
            PlayerInfo[playerid][pJailTimer] = 0;
        }
        return 1;
    }

    PlayerInfo[playerid][pJailTime]--;
    UpdateJailTextDraw(playerid);

    if(PlayerInfo[playerid][pJailTime] <= 0)
    {
        UnjailPlayer(playerid);
    }

    return 1;
}

// Function to put player back in jail after login (called with delay)
public PutPlayerBackInJail(playerid)
{
    // Check if player is still connected and still jailed
    if(!IsPlayerConnected(playerid) || !PlayerInfo[playerid][pLogged] || !PlayerInfo[playerid][pJailed])
        return 1;

    // Check if jail time is still valid
    if(PlayerInfo[playerid][pJailTime] <= 0)
    {
        // Jail time expired, unjail player
        UnjailPlayer(playerid);
        return 1;
    }

    // Get random jail position
    new jailSlot = random(sizeof(JailPositions));

    // Teleport player to jail
    SetPlayerPos(playerid, JailPositions[jailSlot][0], JailPositions[jailSlot][1], JailPositions[jailSlot][2]);
    SetPlayerFacingAngle(playerid, JailPositions[jailSlot][3]);
    SetPlayerVirtualWorld(playerid, 1000);
    SetPlayerInterior(playerid, 0);

    // Update player data
    PlayerInfo[playerid][pVirtualWorld] = 1000;
    PlayerInfo[playerid][pInterior] = 0;

    // Create jail textdraw and start timer
    CreateJailTextDraw(playerid);
    new funcname[] = "OnJailTimerUpdate";
    new formatstr[] = "i";
    PlayerInfo[playerid][pJailTimer] = SetTimerEx(funcname, 1000, true, formatstr, playerid);

    // Notify player
    SendClientMessage(playerid, COLOR_ADMIN, "INFO: Anda masih dalam masa jail OOC. Silakan tunggu hingga waktu selesai.");

    // Show remaining time
    new minutes = PlayerInfo[playerid][pJailTime] / 60;
    new seconds = PlayerInfo[playerid][pJailTime] % 60;
    new timeMsg[128];
    format(timeMsg, sizeof(timeMsg), "JAIL: {FFFFFF}Sisa waktu jail: {FFFF00}%d menit %d detik", minutes, seconds);
    SendClientMessage(playerid, COLOR_KAKZAH, timeMsg);

    return 1;
}

// ========== BAN SYSTEM FUNCTIONS ==========

// Forward declarations for ban system
forward OnBanTimeEnd(playerid);

// Function to ban a player
stock BanPlayer(playerid, days, const reason[], const adminName[])
{
    if(PlayerInfo[playerid][pBanned])
        return 0; // Already banned

    // Set ban data
    PlayerInfo[playerid][pBanned] = true;

    if(days == 0)
    {
        PlayerInfo[playerid][pBanTime] = 0; // Permanent ban
    }
    else
    {
        PlayerInfo[playerid][pBanTime] = gettime() + (days * 86400); // Convert days to seconds
    }

    format(PlayerInfo[playerid][pBanReason], 128, "%s", reason);

    // Add to warns database
    new targetName[MAX_PLAYER_NAME];
    GetPlayerName(playerid, targetName, sizeof(targetName));
    AddPlayerWarn(targetName, "BAN", adminName, reason);

    // Save player data
    SavePlayerData(playerid);

    return 1;
}

// Function to unban a player
stock UnbanPlayer(playerid)
{
    if(!PlayerInfo[playerid][pBanned])
        return 0; // Not banned

    // Reset ban data
    PlayerInfo[playerid][pBanned] = false;
    PlayerInfo[playerid][pBanTime] = 0;
    PlayerInfo[playerid][pBanReason][0] = 0;

    // Save player data
    SavePlayerData(playerid);

    return 1;
}

// Admin command: Kick player
CMD:kick(playerid, params[])
{
    if(!PlayerInfo[playerid][pLogged])
        return SendClientMessage(playerid, 0xFF0000FF, "ERROR: Anda belum login!");

    if(PlayerInfo[playerid][pAdminLevel] < 1)
        return SendClientMessage(playerid, 0xFF0000FF, "ERROR: Hanya admin level 1+ yang bisa menggunakan command ini!");

    new targetid, reason[128];
    if(sscanf(params, "us[128]", targetid, reason))
        return SendClientMessage(playerid, 0x808080FF, "USAGE: /kick [playerid] [reason]");

    if(!IsPlayerConnected(targetid) || !PlayerInfo[targetid][pLogged])
        return SendClientMessage(playerid, 0xFF0000FF, "ERROR: Player tidak online atau belum login!");

    // Get names
    new targetName[MAX_PLAYER_NAME], adminName[32];
    GetPlayerName(targetid, targetName, sizeof(targetName));
    GetPlayerAdminName(playerid, adminName, sizeof(adminName));

    // Add warn to database
    AddPlayerWarn(targetName, "KICK", adminName, reason);

    // Log admin command
    new logStr[256];
    format(logStr, sizeof(logStr), "ADMIN: %s kicked %s (Reason: %s)", adminName, targetName, reason);
    print(logStr);

    // Send message to all players
    new message[256];
    format(message, sizeof(message), "AdmCmd: %s has been kick by Admin %s", targetName, adminName);
    SendClientMessageToAll(COLOR_ADMIN, message);

    format(message, sizeof(message), "Reason: %s", reason);
    SendClientMessageToAll(COLOR_ADMIN, message);

    // Kick the player with delay
    new funcname[] = "DelayedKick";
    new formatstr[] = "i";
    SetTimerEx(funcname, 1000, false, formatstr, targetid);

    return 1;
}

// Function to kick player with delay
forward DelayedKick(playerid);
public DelayedKick(playerid)
{
    if(IsPlayerConnected(playerid))
    {
        Kick(playerid);
    }
    return 1;
}

// Command: Warn player (Admin Level 1+)
CMD:warn(playerid, params[])
{
    if(!PlayerInfo[playerid][pLogged])
        return SendClientMessage(playerid, 0xFF0000FF, "ERROR: Anda belum login!");

    if(PlayerInfo[playerid][pAdminLevel] < 1)
        return SendClientMessage(playerid, 0xFF0000FF, "ERROR: Hanya admin level 1+ yang bisa menggunakan command ini!");

    new targetid, reason[128];
    if(sscanf(params, "us[128]", targetid, reason))
        return SendClientMessage(playerid, 0x808080FF, "USAGE: /warn [playerid] [reason]");

    if(!IsPlayerConnected(targetid) || !PlayerInfo[targetid][pLogged])
        return SendClientMessage(playerid, 0xFF0000FF, "ERROR: Player tidak online atau belum login!");

    // Check if player already has maximum warns
    if(PlayerInfo[targetid][pWarnCount] >= 10)
        return SendClientMessage(playerid, 0xFF0000FF, "ERROR: Player sudah memiliki maksimal warns (10/10)!");

    // Get names
    new targetName[MAX_PLAYER_NAME], adminName[32];
    GetPlayerName(targetid, targetName, sizeof(targetName));
    GetPlayerAdminName(playerid, adminName, sizeof(adminName));

    // Increase warn count
    PlayerInfo[targetid][pWarnCount]++;

    // Add warn to database
    AddPlayerWarn(targetName, "WARN", adminName, reason);

    // Save player data
    SavePlayerData(targetid);

    // Log admin command
    new logStr[256];
    format(logStr, sizeof(logStr), "ADMIN: %s warned %s (Reason: %s) - Total warns: %d", adminName, targetName, reason, PlayerInfo[targetid][pWarnCount]);
    print(logStr);

    // Send message to all players
    new message[256];
    format(message, sizeof(message), "AdmCmd: %s has been warn by Admin %s, Total warn: %d", targetName, adminName, PlayerInfo[targetid][pWarnCount]);
    SendClientMessageToAll(COLOR_ADMIN, message);

    format(message, sizeof(message), "Reason: %s", reason);
    SendClientMessageToAll(COLOR_ADMIN, message);

    return 1;
}

// Command: Remove warn (Admin Level 1+)
CMD:unwarn(playerid, params[])
{
    if(!PlayerInfo[playerid][pLogged])
        return SendClientMessage(playerid, 0xFF0000FF, "ERROR: Anda belum login!");

    if(PlayerInfo[playerid][pAdminLevel] < 1)
        return SendClientMessage(playerid, 0xFF0000FF, "ERROR: Hanya admin level 1+ yang bisa menggunakan command ini!");

    new targetid;
    if(sscanf(params, "u", targetid))
        return SendClientMessage(playerid, 0x808080FF, "USAGE: /unwarn [playerid]");

    if(!IsPlayerConnected(targetid) || !PlayerInfo[targetid][pLogged])
        return SendClientMessage(playerid, 0xFF0000FF, "ERROR: Player tidak online atau belum login!");

    // Check if player has any warns to remove
    if(PlayerInfo[targetid][pWarnCount] <= 0)
        return SendClientMessage(playerid, 0xFF0000FF, "ERROR: Player tidak memiliki warns untuk dihapus!");

    // Get names
    new targetName[MAX_PLAYER_NAME], adminName[32];
    GetPlayerName(targetid, targetName, sizeof(targetName));
    GetPlayerAdminName(playerid, adminName, sizeof(adminName));

    // Decrease warn count
    PlayerInfo[targetid][pWarnCount]--;

    // Save player data
    SavePlayerData(targetid);

    // Log admin command
    new logStr[256];
    format(logStr, sizeof(logStr), "ADMIN: %s removed warn from %s - Total warns: %d", adminName, targetName, PlayerInfo[targetid][pWarnCount]);
    print(logStr);

    // Send message to all players
    new message[256];
    format(message, sizeof(message), "AdmCmd: %s has been remove warn by Admin %s, Total warn: %d", targetName, adminName, PlayerInfo[targetid][pWarnCount]);
    SendClientMessageToAll(COLOR_ADMIN, message);

    return 1;
}

// Command: View player warns
CMD:mywarns(playerid, params[])
{
    if(!PlayerInfo[playerid][pLogged])
        return SendClientMessage(playerid, 0xFF0000FF, "ERROR: Anda belum login!");

    new page = 1;
    if(sscanf(params, "d", page))
        page = 1;

    if(page < 1) page = 1;

    new playerName[MAX_PLAYER_NAME];
    GetPlayerName(playerid, playerName, sizeof(playerName));

    // Store current page
    PlayerWarnsPage[playerid] = page;

    // Query to get player warns with pagination (10 per page)
    new query[256];
    mysql_format(g_SQL, query, sizeof(query),
        "SELECT `warn_type`, `admin_name`, `warn_date`, `reason` FROM `player_warns` WHERE `player_name` = '%s' ORDER BY `id` DESC LIMIT %d, 10",
        playerName, (page - 1) * 10);

    mysql_tquery(g_SQL, query, "ShowPlayerWarns", "i", playerid);

    return 1;
}

// Command: Remove all warns (Admin Level 10 only)
CMD:removeallwarns(playerid, params[])
{
    if(!PlayerInfo[playerid][pLogged])
        return SendClientMessage(playerid, 0xFF0000FF, "ERROR: Anda belum login!");

    if(PlayerInfo[playerid][pAdminLevel] < 10)
        return SendClientMessage(playerid, 0xFF0000FF, "ERROR: Hanya admin level 10 yang bisa menggunakan command ini!");

    new targetName[MAX_PLAYER_NAME];
    if(sscanf(params, "s[24]", targetName))
        return SendClientMessage(playerid, 0x808080FF, "USAGE: /removeallwarns [player_name]");

    // Get admin name
    new adminName[32];
    GetPlayerAdminName(playerid, adminName, sizeof(adminName));

    // Query to remove all warns
    new query[256];
    mysql_format(g_SQL, query, sizeof(query),
        "DELETE FROM `player_warns` WHERE `player_name` = '%s'",
        targetName);

    mysql_tquery(g_SQL, query, "OnWarnsRemoved", "iss", playerid, targetName, adminName);

    return 1;
}

// Command: Check player warns (Admin Level 1)
CMD:checkwarns(playerid, params[])
{
    if(!PlayerInfo[playerid][pLogged])
        return SendClientMessage(playerid, 0xFF0000FF, "ERROR: Anda belum login!");

    if(PlayerInfo[playerid][pAdminLevel] < 1)
        return SendClientMessage(playerid, 0xFF0000FF, "ERROR: Hanya admin level 1+ yang bisa menggunakan command ini!");

    new targetName[MAX_PLAYER_NAME], page = 1;
    if(sscanf(params, "s[24]d", targetName, page))
    {
        if(sscanf(params, "s[24]", targetName))
            return SendClientMessage(playerid, 0x808080FF, "USAGE: /checkwarns [player_name] [page]");
        page = 1;
    }

    if(page < 1) page = 1;

    // Store current page and target name
    PlayerWarnsPage[playerid] = page;
    format(PlayerWarnsTarget[playerid], MAX_PLAYER_NAME, "%s", targetName);

    // Query to get target player warns with pagination (10 per page)
    new query[256];
    mysql_format(g_SQL, query, sizeof(query),
        "SELECT `warn_type`, `admin_name`, `warn_date`, `reason` FROM `player_warns` WHERE `player_name` = '%s' ORDER BY `id` DESC LIMIT %d, 10",
        targetName, (page - 1) * 10);

    mysql_tquery(g_SQL, query, "ShowTargetWarns", "is", playerid, targetName);

    return 1;
}

// Callback: Show player warns
forward ShowPlayerWarns(playerid);
public ShowPlayerWarns(playerid)
{
    new rows = cache_num_rows();

    if(!rows)
    {
        if(PlayerWarnsPage[playerid] == 1)
        {
            SendClientMessage(playerid, COLOR_GREY, "ERROR: Anda tidak memiliki warns/punishment records.");
        }
        else
        {
            SendClientMessage(playerid, COLOR_GREY, "ERROR: Tidak ada data pada halaman ini.");
        }
        return 1;
    }

    new warnsList[2048], tempStr[256];
    new warnType[16], adminName[32], warnDate[32], reason[128];

    // Header for tablist
    strcat(warnsList, "Type\tAdmin\tIssue Date\tReason\n");

    // Reset player warns count and data
    PlayerWarnsCount[playerid] = 0;

    for(new i = 0; i < rows && i < 10; i++)
    {
        cache_get_value_name(i, "warn_type", warnType, sizeof(warnType));
        cache_get_value_name(i, "admin_name", adminName, sizeof(adminName));
        cache_get_value_name(i, "warn_date", warnDate, sizeof(warnDate));
        cache_get_value_name(i, "reason", reason, sizeof(reason));

        // Store in player data for detail view
        format(WarnInfo[playerid][i][wType], 16, "%s", warnType);
        format(WarnInfo[playerid][i][wAdmin], 32, "%s", adminName);
        format(WarnInfo[playerid][i][wDate], 32, "%s", warnDate);
        format(WarnInfo[playerid][i][wReason], 128, "%s", reason);
        PlayerWarnsCount[playerid]++;

        // Format for dialog (truncate reason if too long)
        new shortReason[32];
        if(strlen(reason) > 30)
        {
            strmid(shortReason, reason, 0, 27);
            strcat(shortReason, "...");
        }
        else
        {
            format(shortReason, sizeof(shortReason), "%s", reason);
        }

        // Add color based on warn type
        if(strcmp(warnType, "KICK", true) == 0)
        {
            format(tempStr, sizeof(tempStr), "{FFFF00}[%s]{FFFFFF}\t%s\t%s\t%s\n", warnType, adminName, warnDate, shortReason);
        }
        else if(strcmp(warnType, "WARN", true) == 0)
        {
            format(tempStr, sizeof(tempStr), "{FF6347}[%s]{FFFFFF}\t%s\t%s\t%s\n", warnType, adminName, warnDate, shortReason);
        }
        else if(strcmp(warnType, "JAIL", true) == 0)
        {
            format(tempStr, sizeof(tempStr), "{FF0000}[%s]{FFFFFF}\t%s\t%s\t%s\n", warnType, adminName, warnDate, shortReason);
        }
        else if(strcmp(warnType, "BAN", true) == 0)
        {
            format(tempStr, sizeof(tempStr), "{8B0000}[%s]{FFFFFF}\t%s\t%s\t%s\n", warnType, adminName, warnDate, shortReason);
        }
        else
        {
            format(tempStr, sizeof(tempStr), "[%s]\t%s\t%s\t%s\n", warnType, adminName, warnDate, shortReason);
        }
        strcat(warnsList, tempStr);
    }

    // Add navigation options like origin dialog
    new currentPage = PlayerWarnsPage[playerid];
    new hasBack = (currentPage > 1);
    new hasNext = (rows == 10); // If we got 10 rows, there might be more

    if(hasBack || hasNext)
    {
        strcat(warnsList, "{808080}--- Navigation ---\t-\t-\t-\n");

        if(hasBack && hasNext)
        {
            strcat(warnsList, "{FFFF00}< Back\t-\t-\tNext >");
        }
        else if(hasBack)
        {
            strcat(warnsList, "{FFFF00}< Back\t-\t-\t-");
        }
        else if(hasNext)
        {
            strcat(warnsList, "-\t-\t-\t{FFFF00}Next >");
        }
    }

    // Add pagination info
    new dialogTitle[128];
    format(dialogTitle, sizeof(dialogTitle), "My Warns/Punishments - Page %d", PlayerWarnsPage[playerid]);

    Dialog_Show(playerid, MyWarns, DIALOG_STYLE_TABLIST_HEADERS, dialogTitle, warnsList, "Select", "Close");

    return 1;
}

// Callback: Show target player warns (for admin checkwarns)
forward ShowTargetWarns(playerid, const targetName[]);
public ShowTargetWarns(playerid, const targetName[])
{
    new rows = cache_num_rows();

    if(!rows)
    {
        new message[128];
        if(PlayerWarnsPage[playerid] == 1)
        {
            format(message, sizeof(message), "ERROR: Player %s tidak memiliki warns/punishment records.", targetName);
        }
        else
        {
            format(message, sizeof(message), "ERROR: Player %s tidak ada data pada halaman %d.", targetName, PlayerWarnsPage[playerid]);
        }
        SendClientMessage(playerid, COLOR_GREY, message);
        return 1;
    }

    new warnsList[2048], tempStr[256];
    new warnType[16], adminName[32], warnDate[32], reason[128];

    // Header for tablist
    strcat(warnsList, "Type\tAdmin\tIssue Date\tReason\n");

    // Reset player warns count and data for target viewing
    PlayerWarnsCount[playerid] = 0;

    for(new i = 0; i < rows && i < 10; i++)
    {
        cache_get_value_name(i, "warn_type", warnType, sizeof(warnType));
        cache_get_value_name(i, "admin_name", adminName, sizeof(adminName));
        cache_get_value_name(i, "warn_date", warnDate, sizeof(warnDate));
        cache_get_value_name(i, "reason", reason, sizeof(reason));

        // Store in player data for detail view
        format(WarnInfo[playerid][i][wType], 16, "%s", warnType);
        format(WarnInfo[playerid][i][wAdmin], 32, "%s", adminName);
        format(WarnInfo[playerid][i][wDate], 32, "%s", warnDate);
        format(WarnInfo[playerid][i][wReason], 128, "%s", reason);
        PlayerWarnsCount[playerid]++;

        // Format for dialog (truncate reason if too long)
        new shortReason[32];
        if(strlen(reason) > 30)
        {
            strmid(shortReason, reason, 0, 27);
            strcat(shortReason, "...");
        }
        else
        {
            format(shortReason, sizeof(shortReason), "%s", reason);
        }

        // Add color based on warn type
        if(strcmp(warnType, "KICK", true) == 0)
        {
            format(tempStr, sizeof(tempStr), "{FFFF00}[%s]{FFFFFF}\t%s\t%s\t%s\n", warnType, adminName, warnDate, shortReason);
        }
        else if(strcmp(warnType, "WARN", true) == 0)
        {
            format(tempStr, sizeof(tempStr), "{FF6347}[%s]{FFFFFF}\t%s\t%s\t%s\n", warnType, adminName, warnDate, shortReason);
        }
        else if(strcmp(warnType, "JAIL", true) == 0)
        {
            format(tempStr, sizeof(tempStr), "{FF0000}[%s]{FFFFFF}\t%s\t%s\t%s\n", warnType, adminName, warnDate, shortReason);
        }
        else if(strcmp(warnType, "BAN", true) == 0)
        {
            format(tempStr, sizeof(tempStr), "{8B0000}[%s]{FFFFFF}\t%s\t%s\t%s\n", warnType, adminName, warnDate, shortReason);
        }
        else
        {
            format(tempStr, sizeof(tempStr), "[%s]\t%s\t%s\t%s\n", warnType, adminName, warnDate, shortReason);
        }
        strcat(warnsList, tempStr);
    }

    // Add navigation options like origin dialog
    new currentPage = PlayerWarnsPage[playerid];
    new hasBack = (currentPage > 1);
    new hasNext = (rows == 10); // If we got 10 rows, there might be more

    if(hasBack || hasNext)
    {
        strcat(warnsList, "{808080}--- Navigation ---\t-\t-\t-\n");

        if(hasBack && hasNext)
        {
            strcat(warnsList, "{FFFF00}< Back\t-\t-\tNext >");
        }
        else if(hasBack)
        {
            strcat(warnsList, "{FFFF00}< Back\t-\t-\t-");
        }
        else if(hasNext)
        {
            strcat(warnsList, "-\t-\t-\t{FFFF00}Next >");
        }
    }

    new dialogTitle[128];
    format(dialogTitle, sizeof(dialogTitle), "%s's Warns/Punishments - Page %d", targetName, PlayerWarnsPage[playerid]);
    Dialog_Show(playerid, CheckWarns, DIALOG_STYLE_TABLIST_HEADERS, dialogTitle, warnsList, "Select", "Close");

    return 1;
}

// Callback: Warns removed
forward OnWarnsRemoved(playerid, const targetName[], const adminName[]);
public OnWarnsRemoved(playerid, const targetName[], const adminName[])
{
    new affectedRows = cache_affected_rows();

    if(affectedRows > 0)
    {
        new message[128];
        format(message, sizeof(message), "SUCCESS: %d warns berhasil dihapus untuk player %s.", affectedRows, targetName);
        SendClientMessage(playerid, 0x00FF00FF, message);

        // Log admin action
        new logStr[256];
        format(logStr, sizeof(logStr), "ADMIN: %s removed all warns for %s (%d warns deleted)", adminName, targetName, affectedRows);
        print(logStr);
    }
    else
    {
        new message[128];
        format(message, sizeof(message), "INFO: Player %s tidak memiliki warns untuk dihapus.", targetName);
        SendClientMessage(playerid, 0xFFFF00FF, message);
    }

    return 1;
}

// Dialog: MyWarns pagination and detail view
Dialog:MyWarns(playerid, response, listitem, inputtext[])
{
    if(!response) return 1;

    new currentPage = PlayerWarnsPage[playerid];
    new hasBack = (currentPage > 1);
    new hasNext = true; // We'll check this based on navigation items

    // Calculate navigation items count
    new navigationItems = 0;
    if(hasBack || hasNext) navigationItems = 1; // "--- Navigation ---" separator

    // Check if it's a navigation option (similar to origin dialog)
    if(listitem >= PlayerWarnsCount[playerid] + (navigationItems > 0 ? 1 : 0)) // After warns + separator
    {
        new playerName[MAX_PLAYER_NAME];
        GetPlayerName(playerid, playerName, sizeof(playerName));

        // Check which navigation option was selected
        new navIndex = listitem - PlayerWarnsCount[playerid] - 1; // Subtract warns count and separator

        if(hasBack && hasNext && navIndex == 0) // "< Back" selected
        {
            PlayerWarnsPage[playerid]--;
        }
        else if(hasBack && hasNext && navIndex == 1) // "Next >" selected (when both exist)
        {
            PlayerWarnsPage[playerid]++;
        }
        else if(hasBack && !hasNext && navIndex == 0) // Only "< Back" exists
        {
            PlayerWarnsPage[playerid]--;
        }
        else if(!hasBack && hasNext && navIndex == 0) // Only "Next >" exists
        {
            PlayerWarnsPage[playerid]++;
        }

        // Reload the page
        new query[256];
        mysql_format(g_SQL, query, sizeof(query),
            "SELECT `warn_type`, `admin_name`, `warn_date`, `reason` FROM `player_warns` WHERE `player_name` = '%s' ORDER BY `id` DESC LIMIT %d, 10",
            playerName, (PlayerWarnsPage[playerid] - 1) * 10);

        mysql_tquery(g_SQL, query, "ShowPlayerWarns", "i", playerid);
        return 1;
    }
    else if(listitem < PlayerWarnsCount[playerid]) // Warn selected
    {
        // Show detailed information about selected warn
        new detailText[512];
        format(detailText, sizeof(detailText),
            "{FFFFFF}Punishment Details:\n\n\
            {FFFF00}Type:{FFFFFF} %s\n\
            {FFFF00}Admin:{FFFFFF} %s\n\
            {FFFF00}Date:{FFFFFF} %s\n\
            {FFFF00}Reason:{FFFFFF} %s\n\n\
            {FF6347}This punishment is part of your permanent record.",
            WarnInfo[playerid][listitem][wType],
            WarnInfo[playerid][listitem][wAdmin],
            WarnInfo[playerid][listitem][wDate],
            WarnInfo[playerid][listitem][wReason]);

        Dialog_Show(playerid, MyWarnsDetail, DIALOG_STYLE_MSGBOX, "Punishment Detail", detailText, "Back", "Close");
        return 1;
    }

    return 1;
}


// Dialog: MyWarns detail back to list
Dialog:MyWarnsDetail(playerid, response, listitem, inputtext[])
{
    if(response)
    {
        // Go back to warns list
        new playerName[MAX_PLAYER_NAME];
        GetPlayerName(playerid, playerName, sizeof(playerName));

        new query[256];
        mysql_format(g_SQL, query, sizeof(query),
            "SELECT `warn_type`, `admin_name`, `warn_date`, `reason` FROM `player_warns` WHERE `player_name` = '%s' ORDER BY `id` DESC LIMIT %d, 10",
            playerName, (PlayerWarnsPage[playerid] - 1) * 10);

        mysql_tquery(g_SQL, query, "ShowPlayerWarns", "i", playerid);
    }

    return 1;
}

// Dialog: CheckWarns pagination and detail view (for admin)
Dialog:CheckWarns(playerid, response, listitem, inputtext[])
{
    if(!response) return 1;

    new currentPage = PlayerWarnsPage[playerid];
    new hasBack = (currentPage > 1);
    new hasNext = true; // We'll check this based on navigation items

    // Calculate navigation items count
    new navigationItems = 0;
    if(hasBack || hasNext) navigationItems = 1; // "--- Navigation ---" separator

    // Check if it's a navigation option (similar to origin dialog)
    if(listitem >= PlayerWarnsCount[playerid] + (navigationItems > 0 ? 1 : 0)) // After warns + separator
    {
        new targetName[MAX_PLAYER_NAME];
        format(targetName, sizeof(targetName), "%s", PlayerWarnsTarget[playerid]);

        // Check which navigation option was selected
        new navIndex = listitem - PlayerWarnsCount[playerid] - 1; // Subtract warns count and separator

        if(hasBack && hasNext && navIndex == 0) // "< Back" selected
        {
            PlayerWarnsPage[playerid]--;
        }
        else if(hasBack && hasNext && navIndex == 1) // "Next >" selected (when both exist)
        {
            PlayerWarnsPage[playerid]++;
        }
        else if(hasBack && !hasNext && navIndex == 0) // Only "< Back" exists
        {
            PlayerWarnsPage[playerid]--;
        }
        else if(!hasBack && hasNext && navIndex == 0) // Only "Next >" exists
        {
            PlayerWarnsPage[playerid]++;
        }

        // Reload the page
        new query[256];
        mysql_format(g_SQL, query, sizeof(query),
            "SELECT `warn_type`, `admin_name`, `warn_date`, `reason` FROM `player_warns` WHERE `player_name` = '%s' ORDER BY `id` DESC LIMIT %d, 10",
            targetName, (PlayerWarnsPage[playerid] - 1) * 10);

        mysql_tquery(g_SQL, query, "ShowTargetWarns", "is", playerid, targetName);
        return 1;
    }
    else if(listitem < PlayerWarnsCount[playerid]) // Warn selected
    {
        // Show detailed information about selected warn
        new detailText[512];
        format(detailText, sizeof(detailText),
            "{FFFFFF}Punishment Details:\n\n\
            {FFFF00}Type:{FFFFFF} %s\n\
            {FFFF00}Admin:{FFFFFF} %s\n\
            {FFFF00}Date:{FFFFFF} %s\n\
            {FFFF00}Reason:{FFFFFF} %s\n\n\
            {FF6347}This is an admin view of player's punishment record.",
            WarnInfo[playerid][listitem][wType],
            WarnInfo[playerid][listitem][wAdmin],
            WarnInfo[playerid][listitem][wDate],
            WarnInfo[playerid][listitem][wReason]);

        Dialog_Show(playerid, CheckWarnsDetail, DIALOG_STYLE_MSGBOX, "Punishment Detail", detailText, "Back", "Close");
        return 1;
    }

    return 1;
}



// Dialog: CheckWarns detail back to list (for admin)
Dialog:CheckWarnsDetail(playerid, response, listitem, inputtext[])
{
    if(response)
    {
        // Go back to warns list using stored target name
        new targetName[MAX_PLAYER_NAME];
        format(targetName, sizeof(targetName), "%s", PlayerWarnsTarget[playerid]);

        new query[256];
        mysql_format(g_SQL, query, sizeof(query),
            "SELECT `warn_type`, `admin_name`, `warn_date`, `reason` FROM `player_warns` WHERE `player_name` = '%s' ORDER BY `id` DESC LIMIT %d, 10",
            targetName, (PlayerWarnsPage[playerid] - 1) * 10);

        mysql_tquery(g_SQL, query, "ShowTargetWarns", "is", playerid, targetName);
    }

    return 1;
}

// Dialog: Ban message (player will be kicked)
Dialog:BanMessage(playerid, response, listitem, inputtext[])
{
    // Player will be kicked anyway, no action needed
    return 1;
}

Dialog:HealthStatus(playerid, response, listitem, inputtext[])
{
    // No action needed, just close the dialog
    return 1;
}

Dialog:InventoryMain(playerid, response, listitem, inputtext[])
{
    if(!response) return 1; // Close clicked

    // Skip money item (listitem 0)
    if(listitem == 0) return 1;

    // Find selected weapon
    new currentItem = 0;
    new selectedWeaponSlot = -1;

    // Skip money (item 0)
    currentItem++;

    for(new i = 0; i < 13; i++)
    {
        if(PlayerInfo[playerid][pInventoryWeapons][i] != 0)
        {
            if(currentItem == listitem)
            {
                selectedWeaponSlot = i;
                break;
            }
            currentItem++;

            // Skip ammo item if it exists
            if(PlayerInfo[playerid][pInventoryAmmoUnits][i] > 0)
            {
                if(currentItem == listitem)
                {
                    // Selected ammo item, show weapon menu instead
                    selectedWeaponSlot = i;
                    break;
                }
                currentItem++;
            }
        }
    }

    if(selectedWeaponSlot != -1)
    {
        ShowWeaponActionDialog(playerid, selectedWeaponSlot);
    }

    return 1;
}

Dialog:WeaponAction(playerid, response, listitem, inputtext[])
{
    if(!response)
    {
        ShowInventoryDialog(playerid); // Go back to inventory
        return 1;
    }

    if(listitem == 0) // Reload Ammo or No Ammo Units
    {
        new weaponSlot = PlayerInfo[playerid][pSelectedWeaponSlot];
        new weaponid = PlayerInfo[playerid][pInventoryWeapons][weaponSlot];

        // Check if player has ammo units
        if(PlayerInfo[playerid][pInventoryAmmoUnits][weaponSlot] > 0)
        {
            if(ReloadWeaponFromInventory(playerid, weaponid))
            {
                ShowInventoryDialog(playerid); // Refresh inventory
            }
        }
        else
        {
            SendClientMessage(playerid, 0xFF0000FF, "RELOAD: {FFFFFF}You don't have any ammo units for this weapon!");
            ShowInventoryDialog(playerid); // Go back to inventory
        }
    }

    return 1;
}

Dialog:AdminInventory(playerid, response, listitem, inputtext[])
{
    // No action needed, just close the dialog
    return 1;
}

// ========== JAIL COMMANDS ==========

// Command: Jail player (Admin Level 1+)
CMD:jail(playerid, params[])
{
    if(!PlayerInfo[playerid][pLogged])
        return SendClientMessage(playerid, 0xFF0000FF, "ERROR: Anda belum login!");

    if(PlayerInfo[playerid][pAdminLevel] < 1)
        return SendClientMessage(playerid, 0xFF0000FF, "ERROR: Hanya admin level 1+ yang bisa menggunakan command ini!");

    new targetid, minutes, reason[128];
    if(sscanf(params, "uds[128]", targetid, minutes, reason))
        return SendClientMessage(playerid, 0x808080FF, "USAGE: /jail [playerid] [minute] [reason]");

    if(!IsPlayerConnected(targetid) || !PlayerInfo[targetid][pLogged])
        return SendClientMessage(playerid, 0xFF0000FF, "ERROR: Player tidak online atau belum login!");

    if(PlayerInfo[targetid][pJailed])
        return SendClientMessage(playerid, 0xFF0000FF, "ERROR: Player sudah di jail!");

    if(minutes < 1 || minutes > 1440) // Max 24 hours
        return SendClientMessage(playerid, 0xFF0000FF, "ERROR: Waktu jail harus 1-1440 menit (max 24 jam)!");

    // Get names
    new targetName[MAX_PLAYER_NAME], adminName[32];
    GetPlayerName(targetid, targetName, sizeof(targetName));
    GetPlayerAdminName(playerid, adminName, sizeof(adminName));

    // Jail the player
    if(JailPlayer(targetid, minutes, reason, adminName))
    {
        // Send messages
        new message[256];
        format(message, sizeof(message), "AdmCmd: %s has been jail by %s, for: %d minut(e)", targetName, adminName, minutes);
        SendClientMessageToAll(COLOR_ADMIN, message);

        format(message, sizeof(message), "Reason: %s", reason);
        SendClientMessageToAll(COLOR_ADMIN, message);

        // Message to jailed player
        format(message, sizeof(message), "Anda telah di jail selama %d menit oleh Admin %s", minutes, adminName);
        SendClientMessage(targetid, 0xFF0000FF, message);
        format(message, sizeof(message), "Alasan: %s", reason);
        SendClientMessage(targetid, 0xFF0000FF, message);

        // Log admin command
        new logStr[256];
        format(logStr, sizeof(logStr), "ADMIN: %s jailed %s for %d minutes (Reason: %s)", adminName, targetName, minutes, reason);
        print(logStr);
    }
    else
    {
        SendClientMessage(playerid, 0xFF0000FF, "ERROR: Gagal jail player!");
    }

    return 1;
}

// Command: Offline jail player (Admin Level 1+)
CMD:ojail(playerid, params[])
{
    if(!PlayerInfo[playerid][pLogged])
        return SendClientMessage(playerid, 0xFF0000FF, "ERROR: Anda belum login!");

    if(PlayerInfo[playerid][pAdminLevel] < 1)
        return SendClientMessage(playerid, 0xFF0000FF, "ERROR: Hanya admin level 1+ yang bisa menggunakan command ini!");

    new targetName[MAX_PLAYER_NAME], minutes, reason[128];
    if(sscanf(params, "s[24]ds[128]", targetName, minutes, reason))
        return SendClientMessage(playerid, 0x808080FF, "USAGE: /ojail [player_name] [minute] [reason]");

    if(minutes < 1 || minutes > 1440) // Max 24 hours
        return SendClientMessage(playerid, 0xFF0000FF, "ERROR: Waktu jail harus 1-1440 menit (max 24 jam)!");

    // Get admin name
    new adminName[32];
    GetPlayerAdminName(playerid, adminName, sizeof(adminName));

    // Check if player exists and jail them in database
    new query[512];
    mysql_format(g_SQL, query, sizeof(query),
        "UPDATE `players` SET `jailed` = 1, `jail_time` = %d WHERE `username` = '%e'",
        minutes * 60, targetName);

    mysql_tquery(g_SQL, query, "OnOfflineJail", "issis", playerid, targetName, adminName, minutes, reason);

    return 1;
}

// Command: Unjail player (Admin Level 1+)
CMD:release(playerid, params[])
{
    if(!PlayerInfo[playerid][pLogged])
        return SendClientMessage(playerid, 0xFF0000FF, "ERROR: Anda belum login!");

    if(PlayerInfo[playerid][pAdminLevel] < 1)
        return SendClientMessage(playerid, 0xFF0000FF, "ERROR: Hanya admin level 1+ yang bisa menggunakan command ini!");

    new targetid;
    if(sscanf(params, "u", targetid))
        return SendClientMessage(playerid, 0x808080FF, "USAGE: /unjail [playerid]");

    if(!IsPlayerConnected(targetid) || !PlayerInfo[targetid][pLogged])
        return SendClientMessage(playerid, 0xFF0000FF, "ERROR: Player tidak online atau belum login!");

    if(!PlayerInfo[targetid][pJailed])
        return SendClientMessage(playerid, 0xFF0000FF, "ERROR: Player tidak di jail!");

    // Get names
    new targetName[MAX_PLAYER_NAME], adminName[32];
    GetPlayerName(targetid, targetName, sizeof(targetName));
    GetPlayerAdminName(playerid, adminName, sizeof(adminName));

    // Unjail the player
    if(UnjailPlayer(targetid))
    {
        // Send messages
        new message[256];
        format(message, sizeof(message), "AdmCmd: %s has been release jail by Admin %s", targetName, adminName);
        SendClientMessageToAll(COLOR_ADMIN, message);

        // Log admin command
        new logStr[256];
        format(logStr, sizeof(logStr), "ADMIN: %s release jail %s", adminName, targetName);
        print(logStr);
    }
    else
    {
        SendClientMessage(playerid, 0xFF0000FF, "ERROR: Gagal unjail player!");
    }

    return 1;
}

// ========== TIME AND WEATHER SYSTEM ==========

// Forward declarations for time and weather system
forward UpdateWeatherSystem();
forward UpdateTimeForAllPlayers();

// Function to create time textdraw for player
stock CreateTimeTextDraw(playerid)
{
    // Destroy existing textdraws first to prevent doubles
    DestroyTimeTextDraw(playerid);

    // Get current real-world time
    new year, month, day, hour, minute, second;
    getdate(year, month, day);
    gettime(hour, minute, second);

    // Get day name in Indonesian using accurate calculation
    new dayName[10];
    GetAccurateDayName(day, month, year, dayName, sizeof(dayName));

    // Get month name in Indonesian
    new monthName[15];
    GetMonthName(month, monthName, sizeof(monthName));

    // Create date textdraw
    new dateString[64];
    format(dateString, sizeof(dateString), "%s, %02d %s %d", dayName, day, monthName, year);
    PlayerInfo[playerid][pTimeTextDraw][0] = CreatePlayerTextDraw(playerid, 606.000000, 4.000000, dateString);
    PlayerTextDrawFont(playerid, PlayerInfo[playerid][pTimeTextDraw][0], 1);
    PlayerTextDrawLetterSize(playerid, PlayerInfo[playerid][pTimeTextDraw][0], 0.208333, 1.799998);
    PlayerTextDrawTextSize(playerid, PlayerInfo[playerid][pTimeTextDraw][0], 405.500000, 19.500000);
    PlayerTextDrawSetOutline(playerid, PlayerInfo[playerid][pTimeTextDraw][0], 1);
    PlayerTextDrawSetShadow(playerid, PlayerInfo[playerid][pTimeTextDraw][0], 0);
    PlayerTextDrawAlignment(playerid, PlayerInfo[playerid][pTimeTextDraw][0], 3);
    PlayerTextDrawColor(playerid, PlayerInfo[playerid][pTimeTextDraw][0], -1);
    PlayerTextDrawBackgroundColor(playerid, PlayerInfo[playerid][pTimeTextDraw][0], 255);
    PlayerTextDrawBoxColor(playerid, PlayerInfo[playerid][pTimeTextDraw][0], 50);
    PlayerTextDrawUseBox(playerid, PlayerInfo[playerid][pTimeTextDraw][0], 0);
    PlayerTextDrawSetProportional(playerid, PlayerInfo[playerid][pTimeTextDraw][0], 1);
    PlayerTextDrawSetSelectable(playerid, PlayerInfo[playerid][pTimeTextDraw][0], 1);

    // Create time textdraw
    new timeString[16];
    format(timeString, sizeof(timeString), "%02d:%02d:%02d", hour, minute, second);
    PlayerInfo[playerid][pTimeTextDraw][1] = CreatePlayerTextDraw(playerid, 575.000000, 20.000000, timeString);
    PlayerTextDrawFont(playerid, PlayerInfo[playerid][pTimeTextDraw][1], 1);
    PlayerTextDrawLetterSize(playerid, PlayerInfo[playerid][pTimeTextDraw][1], 0.208333, 1.799998);
    PlayerTextDrawTextSize(playerid, PlayerInfo[playerid][pTimeTextDraw][1], 405.500000, 19.500000);
    PlayerTextDrawSetOutline(playerid, PlayerInfo[playerid][pTimeTextDraw][1], 1);
    PlayerTextDrawSetShadow(playerid, PlayerInfo[playerid][pTimeTextDraw][1], 0);
    PlayerTextDrawAlignment(playerid, PlayerInfo[playerid][pTimeTextDraw][1], 2);
    PlayerTextDrawColor(playerid, PlayerInfo[playerid][pTimeTextDraw][1], -1);
    PlayerTextDrawBackgroundColor(playerid, PlayerInfo[playerid][pTimeTextDraw][1], 255);
    PlayerTextDrawBoxColor(playerid, PlayerInfo[playerid][pTimeTextDraw][1], 50);
    PlayerTextDrawUseBox(playerid, PlayerInfo[playerid][pTimeTextDraw][1], 0);
    PlayerTextDrawSetProportional(playerid, PlayerInfo[playerid][pTimeTextDraw][1], 1);
    PlayerTextDrawSetSelectable(playerid, PlayerInfo[playerid][pTimeTextDraw][1], 1);

    // Show both textdraws
    PlayerTextDrawShow(playerid, PlayerInfo[playerid][pTimeTextDraw][0]);
    PlayerTextDrawShow(playerid, PlayerInfo[playerid][pTimeTextDraw][1]);
}

// Function to destroy time textdraw
stock DestroyTimeTextDraw(playerid)
{
    for(new i = 0; i < 2; i++)
    {
        if(PlayerInfo[playerid][pTimeTextDraw][i] != PlayerText:INVALID_TEXT_DRAW)
        {
            PlayerTextDrawHide(playerid, PlayerInfo[playerid][pTimeTextDraw][i]);
            PlayerTextDrawDestroy(playerid, PlayerInfo[playerid][pTimeTextDraw][i]);
            PlayerInfo[playerid][pTimeTextDraw][i] = PlayerText:INVALID_TEXT_DRAW;
        }
    }
}

// Function to update time textdraw for a player
stock UpdateTimeTextDraw(playerid)
{
    if(PlayerInfo[playerid][pTimeTextDraw][0] != PlayerText:INVALID_TEXT_DRAW &&
       PlayerInfo[playerid][pTimeTextDraw][1] != PlayerText:INVALID_TEXT_DRAW)
    {
        // Get current real-world time
        new year, month, day, hour, minute, second;
        getdate(year, month, day);
        gettime(hour, minute, second);

        // Get day name in Indonesian using accurate calculation
        new dayName[10];
        GetAccurateDayName(day, month, year, dayName, sizeof(dayName));

        // Get month name in Indonesian
        new monthName[15];
        GetMonthName(month, monthName, sizeof(monthName));

        // Update date textdraw
        new dateString[64];
        format(dateString, sizeof(dateString), "%s, %02d %s %d", dayName, day, monthName, year);
        PlayerTextDrawSetString(playerid, PlayerInfo[playerid][pTimeTextDraw][0], dateString);

        // Update time textdraw
        new timeString[16];
        format(timeString, sizeof(timeString), "%02d:%02d:%02d", hour, minute, second);
        PlayerTextDrawSetString(playerid, PlayerInfo[playerid][pTimeTextDraw][1], timeString);
    }
}

// Function to get accurate day name using Zeller's Congruence algorithm
stock GetAccurateDayName(day, month, year, output[], maxlength)
{
    new dayNames[7][10] = {
        "Sabtu", "Minggu", "Senin", "Selasa", "Rabu", "Kamis", "Jumat"
    };

    // Zeller's Congruence algorithm for accurate day calculation
    new q = day;
    new m = month;
    new k = year % 100;
    new j = year / 100;

    // In Zeller's algorithm, January and February are counted as months 13 and 14 of the previous year
    if(m < 3)
    {
        m += 12;
        if(k == 0)
        {
            k = 99;
            j--;
        }
        else
        {
            k--;
        }
    }

    // Zeller's formula
    new h = (q + ((13 * (m + 1)) / 5) + k + (k / 4) + (j / 4) - 2 * j) % 7;

    // Ensure positive result
    if(h < 0) h += 7;

    format(output, maxlength, "%s", dayNames[h]);
    return 1;
}

// Timer callback to update time for all players (called every second)
public UpdateTimeForAllPlayers()
{
    // Get current real-world time
    new hour, minute, second;
    gettime(hour, minute, second);

    // Set SA-MP world time to match real time (for day/night cycle)
    SetWorldTime(hour);

    // Update textdraws for all players
    for(new i = 0; i < MAX_PLAYERS; i++)
    {
        if(IsPlayerConnected(i) && PlayerInfo[i][pLogged])
        {
            UpdateTimeTextDraw(i);
        }
    }
    return 1;
}

// Timer callback to update weather system (called every minute)
public UpdateWeatherSystem()
{
    new hour, minute, second;
    gettime(hour, minute, second);

    // Change weather every hour when minute = 0
    if(minute == 0 && gLastWeatherChange != hour)
    {
        // Update last change tracker
        gLastWeatherChange = hour;

        // Get random weather from the list
        new randomIndex = random(sizeof(gWeatherList));
        new newWeather = gWeatherList[randomIndex];

        // Make sure we get a different weather (avoid same weather twice)
        new attempts = 0;
        while(newWeather == gCurrentWeather && sizeof(gWeatherList) > 1 && attempts < 10)
        {
            randomIndex = random(sizeof(gWeatherList));
            newWeather = gWeatherList[randomIndex];
            attempts++;
        }

        gCurrentWeather = newWeather;

        // Set weather for all players
        SetWeather(gCurrentWeather);

        printf("Weather changed to ID %d at %02d:00", gCurrentWeather, hour);
    }
    return 1;
}

// ========== BAN COMMANDS ==========

// Command: Ban player (Admin Level 1+)
CMD:ban(playerid, params[])
{
    if(!PlayerInfo[playerid][pLogged])
        return SendClientMessage(playerid, 0xFF0000FF, "ERROR: Anda belum login!");

    if(PlayerInfo[playerid][pAdminLevel] < 1)
        return SendClientMessage(playerid, 0xFF0000FF, "ERROR: Hanya admin level 1+ yang bisa menggunakan command ini!");

    new targetid, days, reason[128];
    if(sscanf(params, "uds[128]", targetid, days, reason))
        return SendClientMessage(playerid, 0x808080FF, "USAGE: /ban [playerid] [days] [reason] (0 = permanent)");

    if(!IsPlayerConnected(targetid) || !PlayerInfo[targetid][pLogged])
        return SendClientMessage(playerid, 0xFF0000FF, "ERROR: Player tidak online atau belum login!");

    if(PlayerInfo[targetid][pBanned])
        return SendClientMessage(playerid, 0xFF0000FF, "ERROR: Player sudah di ban!");

    if(days < 0 || days > 365) // Max 1 year
        return SendClientMessage(playerid, 0xFF0000FF, "ERROR: Hari ban harus 0-365 (0 = permanent)!");

    // Get names
    new targetName[MAX_PLAYER_NAME], adminName[32];
    GetPlayerName(targetid, targetName, sizeof(targetName));
    GetPlayerAdminName(playerid, adminName, sizeof(adminName));

    // Ban the player
    if(BanPlayer(targetid, days, reason, adminName))
    {
        // Send messages
        new message[256];
        if(days == 0)
        {
            format(message, sizeof(message), "AdmCmd: %s has been banned by Admin %s", targetName, adminName);
        }
        else
        {
            format(message, sizeof(message), "AdmCmd: %s has been banned by Admin %s for %d day(s)", targetName, adminName, days);
        }
        SendClientMessageToAll(COLOR_ADMIN, message);

        format(message, sizeof(message), "Reason: %s", reason);
        SendClientMessageToAll(COLOR_ADMIN, message);

        // Message to banned player
        if(days == 0)
        {
            format(message, sizeof(message), "Anda telah di ban permanent oleh Admin %s", adminName);
        }
        else
        {
            format(message, sizeof(message), "Anda telah di ban selama %d hari oleh Admin %s", days, adminName);
        }
        SendClientMessage(targetid, 0xFF0000FF, message);
        format(message, sizeof(message), "Alasan: %s", reason);
        SendClientMessage(targetid, 0xFF0000FF, message);

        // Log admin command
        new logStr[256];
        if(days == 0)
        {
            format(logStr, sizeof(logStr), "ADMIN: %s permanently banned %s (Reason: %s)", adminName, targetName, reason);
        }
        else
        {
            format(logStr, sizeof(logStr), "ADMIN: %s banned %s for %d days (Reason: %s)", adminName, targetName, days, reason);
        }
        print(logStr);

        // Kick player after 3 seconds
        new funcname[] = "KickPlayerDelayed";
        new formatstr[] = "i";
        SetTimerEx(funcname, 3000, false, formatstr, targetid);
    }
    else
    {
        SendClientMessage(playerid, 0xFF0000FF, "ERROR: Gagal ban player!");
    }

    return 1;
}

// Command: Offline ban player (Admin Level 1+)
CMD:oban(playerid, params[])
{
    if(!PlayerInfo[playerid][pLogged])
        return SendClientMessage(playerid, 0xFF0000FF, "ERROR: Anda belum login!");

    if(PlayerInfo[playerid][pAdminLevel] < 1)
        return SendClientMessage(playerid, 0xFF0000FF, "ERROR: Hanya admin level 1+ yang bisa menggunakan command ini!");

    new targetName[MAX_PLAYER_NAME], days, reason[128];
    if(sscanf(params, "s[24]ds[128]", targetName, days, reason))
        return SendClientMessage(playerid, 0x808080FF, "USAGE: /oban [player_name] [days] [reason] (0 = permanent)");

    if(days < 0 || days > 365) // Max 1 year
        return SendClientMessage(playerid, 0xFF0000FF, "ERROR: Hari ban harus 0-365 (0 = permanent)!");

    // Get admin name
    new adminName[32];
    GetPlayerAdminName(playerid, adminName, sizeof(adminName));

    // Check if player exists and ban them in database
    new query[512];
    new banTime = (days == 0) ? 0 : (gettime() + (days * 86400));

    mysql_format(g_SQL, query, sizeof(query),
        "UPDATE `players` SET `banned` = 1, `ban_time` = %d, `ban_reason` = '%e' WHERE `username` = '%e'",
        banTime, reason, targetName);

    mysql_tquery(g_SQL, query, "OnOfflineBan", "isssi", playerid, targetName, adminName, reason, days);

    return 1;
}

// Command: Unban player (Admin Level 1+)
CMD:unban(playerid, params[])
{
    if(!PlayerInfo[playerid][pLogged])
        return SendClientMessage(playerid, 0xFF0000FF, "ERROR: Anda belum login!");

    if(PlayerInfo[playerid][pAdminLevel] < 1)
        return SendClientMessage(playerid, 0xFF0000FF, "ERROR: Hanya admin level 1+ yang bisa menggunakan command ini!");

    new targetid;
    if(sscanf(params, "u", targetid))
        return SendClientMessage(playerid, 0x808080FF, "USAGE: /unban [playerid]");

    if(!IsPlayerConnected(targetid) || !PlayerInfo[targetid][pLogged])
        return SendClientMessage(playerid, 0xFF0000FF, "ERROR: Player tidak online atau belum login!");

    if(!PlayerInfo[targetid][pBanned])
        return SendClientMessage(playerid, 0xFF0000FF, "ERROR: Player tidak di ban!");

    // Get names
    new targetName[MAX_PLAYER_NAME], adminName[32];
    GetPlayerName(targetid, targetName, sizeof(targetName));
    GetPlayerAdminName(playerid, adminName, sizeof(adminName));

    // Unban the player
    if(UnbanPlayer(targetid))
    {
        // Send messages
        new message[256];
        format(message, sizeof(message), "AdmCmd: %s has been unbanned by Admin %s", targetName, adminName);
        SendClientMessageToAll(COLOR_ADMIN, message);

        // Log admin command
        new logStr[256];
        format(logStr, sizeof(logStr), "ADMIN: %s unbanned %s", adminName, targetName);
        print(logStr);
    }
    else
    {
        SendClientMessage(playerid, 0xFF0000FF, "ERROR: Gagal unban player!");
    }

    return 1;
}

// Command: Offline unban player (Admin Level 1+)
CMD:ounban(playerid, params[])
{
    if(!PlayerInfo[playerid][pLogged])
        return SendClientMessage(playerid, 0xFF0000FF, "ERROR: Anda belum login!");

    if(PlayerInfo[playerid][pAdminLevel] < 1)
        return SendClientMessage(playerid, 0xFF0000FF, "ERROR: Hanya admin level 1+ yang bisa menggunakan command ini!");

    new targetName[MAX_PLAYER_NAME];
    if(sscanf(params, "s[24]", targetName))
        return SendClientMessage(playerid, 0x808080FF, "USAGE: /ounban [player_name]");

    // Get admin name
    new adminName[32];
    GetPlayerAdminName(playerid, adminName, sizeof(adminName));

    // Check if player exists and unban them in database
    new query[512];
    mysql_format(g_SQL, query, sizeof(query),
        "UPDATE `players` SET `banned` = 0, `ban_time` = 0, `ban_reason` = '' WHERE `username` = '%e' AND `banned` = 1",
        targetName);

    mysql_tquery(g_SQL, query, "OnOfflineUnban", "iss", playerid, targetName, adminName);

    return 1;
}

// Command: Set Virtual World (Admin Level 2+)
CMD:setvw(playerid, params[])
{
    if(!PlayerInfo[playerid][pLogged])
        return SendClientMessage(playerid, 0xFF0000FF, "ERROR: Anda belum login!");

    if(PlayerInfo[playerid][pAdminLevel] < 2)
        return SendClientMessage(playerid, 0xFF0000FF, "ERROR: Hanya admin level 2+ yang bisa menggunakan command ini!");

    new targetid, virtualworld;
    if(sscanf(params, "ud", targetid, virtualworld))
        return SendClientMessage(playerid, 0x808080FF, "USAGE: /setvw [playerid] [virtual_world]");

    if(!IsPlayerConnected(targetid) || !PlayerInfo[targetid][pLogged])
        return SendClientMessage(playerid, 0xFF0000FF, "ERROR: Player tidak online atau belum login!");

    if(virtualworld < 0 || virtualworld > 2147483647)
        return SendClientMessage(playerid, 0xFF0000FF, "ERROR: Virtual world harus 0-2147483647!");

    // Get names
    new targetName[MAX_PLAYER_NAME], adminName[32];
    GetPlayerName(targetid, targetName, sizeof(targetName));
    GetPlayerAdminName(playerid, adminName, sizeof(adminName));

    // Set virtual world
    SetPlayerVirtualWorld(targetid, virtualworld);
    PlayerInfo[targetid][pVirtualWorld] = virtualworld;

    // Save player data
    SavePlayerData(targetid);

    // Send messages
    new message[256];
    format(message, sizeof(message), "AdmCmd: %s's virtual world has been set to %d by Admin %s", targetName, virtualworld, adminName);
    SendClientMessageToAll(COLOR_ADMIN, message);

    format(message, sizeof(message), "INFO: {FFFFFF}Virtual world Anda telah diubah menjadi {FFFF00}%d {FFFFFF}oleh Admin {FF0000}%s", virtualworld, adminName);
    SendClientMessage(targetid, COLOR_KAKZAH, message);

    // Log admin command
    new logStr[256];
    format(logStr, sizeof(logStr), "ADMIN: %s set %s's virtual world to %d", adminName, targetName, virtualworld);
    print(logStr);

    return 1;
}

// Command: Set Interior (Admin Level 2+)
CMD:setint(playerid, params[])
{
    if(!PlayerInfo[playerid][pLogged])
        return SendClientMessage(playerid, 0xFF0000FF, "ERROR: Anda belum login!");

    if(PlayerInfo[playerid][pAdminLevel] < 5)
        return SendClientMessage(playerid, 0xFF0000FF, "ERROR: Hanya admin level 5+ yang bisa menggunakan command ini!");

    new targetid, interior;
    if(sscanf(params, "ud", targetid, interior))
        return SendClientMessage(playerid, 0x808080FF, "USAGE: /setint [playerid] [interior]");

    if(!IsPlayerConnected(targetid) || !PlayerInfo[targetid][pLogged])
        return SendClientMessage(playerid, 0xFF0000FF, "ERROR: Player tidak online atau belum login!");

    if(interior < 0 || interior > 255)
        return SendClientMessage(playerid, 0xFF0000FF, "ERROR: Interior harus 0-255!");

    // Get names
    new targetName[MAX_PLAYER_NAME], adminName[32];
    GetPlayerName(targetid, targetName, sizeof(targetName));
    GetPlayerAdminName(playerid, adminName, sizeof(adminName));

    // Set interior
    SetPlayerInterior(targetid, interior);
    PlayerInfo[targetid][pInterior] = interior;

    // Save player data
    SavePlayerData(targetid);

    // Send messages
    new message[256];
    format(message, sizeof(message), "AdmCmd: %s's interior has been set to %d by Admin %s", targetName, interior, adminName);
    SendClientMessageToAll(COLOR_ADMIN, message);

    format(message, sizeof(message), "INFO: {FFFFFF}Interior Anda telah diubah menjadi {FFFF00}%d {FFFFFF}oleh Admin {FF0000}%s", interior, adminName);
    SendClientMessage(targetid, COLOR_KAKZAH, message);

    // Log admin command
    new logStr[256];
    format(logStr, sizeof(logStr), "ADMIN: %s set %s's interior to %d", adminName, targetName, interior);
    print(logStr);

    return 1;
}

// Command: Set Weather (Admin Level 2+)
CMD:setweather(playerid, params[])
{
    if(!PlayerInfo[playerid][pLogged])
        return SendClientMessage(playerid, 0xFF0000FF, "ERROR: Anda belum login!");

    if(PlayerInfo[playerid][pAdminLevel] < 2)
        return SendClientMessage(playerid, 0xFF0000FF, "ERROR: Hanya admin level 2+ yang bisa menggunakan command ini!");

    new weatherid;
    if(sscanf(params, "d", weatherid))
        return SendClientMessage(playerid, 0x808080FF, "USAGE: /setweather [weather_id] (0-20)");

    if(weatherid < 0 || weatherid > 20)
        return SendClientMessage(playerid, 0xFF0000FF, "ERROR: Weather ID harus 0-20!");

    // Get admin name
    new adminName[32];
    GetPlayerAdminName(playerid, adminName, sizeof(adminName));

    // Set weather for all players
    gCurrentWeather = weatherid;
    SetWeather(gCurrentWeather);

    // Send message
    new message[256];
    format(message, sizeof(message), "INFO: {FFFFFF}Cuaca telah diubah menjadi ID {FFFF00}%d {FFFFFF}oleh Admin {FF0000}%s", weatherid, adminName);
    SendClientMessageToAll(COLOR_KAKZAH, message);

    // Log admin command
    new logStr[256];
    format(logStr, sizeof(logStr), "ADMIN: %s changed weather to ID %d", adminName, weatherid);
    print(logStr);

    return 1;
}

// Command: Offline warn player (Admin Level 1+)
CMD:owarn(playerid, params[])
{
    if(!PlayerInfo[playerid][pLogged])
        return SendClientMessage(playerid, 0xFF0000FF, "ERROR: Anda belum login!");

    if(PlayerInfo[playerid][pAdminLevel] < 1)
        return SendClientMessage(playerid, 0xFF0000FF, "ERROR: Hanya admin level 1+ yang bisa menggunakan command ini!");

    new targetName[MAX_PLAYER_NAME], reason[128];
    if(sscanf(params, "s[24]s[128]", targetName, reason))
        return SendClientMessage(playerid, 0x808080FF, "USAGE: /owarn [player_name] [reason]");

    // Get admin name
    new adminName[32];
    GetPlayerAdminName(playerid, adminName, sizeof(adminName));

    // Check if player exists and get current warn count
    new query[512];
    mysql_format(g_SQL, query, sizeof(query),
        "SELECT `id`, `warn_count` FROM `players` WHERE `username` = '%e'",
        targetName);

    mysql_tquery(g_SQL, query, "OnOfflineWarn", "isss", playerid, targetName, adminName, reason);

    return 1;
}

// Callback: Offline jail
forward OnOfflineJail(playerid, const targetName[], const adminName[], minutes, const reason[]);
public OnOfflineJail(playerid, const targetName[], const adminName[], minutes, const reason[])
{
    new affectedRows = cache_affected_rows();

    if(affectedRows > 0)
    {
        // Add warn to database
        AddPlayerWarn(targetName, "JAIL", adminName, reason);

        // Send messages
        new message[256];
        format(message, sizeof(message), "AdmCmd: %s (Offline) has been jail by %s, for: %d minut(e)", targetName, adminName, minutes);
        SendClientMessageToAll(COLOR_ADMIN, message);

        format(message, sizeof(message), "Reason: %s", reason);
        SendClientMessageToAll(COLOR_ADMIN, message);

        // Confirmation to admin
        format(message, sizeof(message), "SUCCESS: Player %s berhasil di jail offline selama %d menit.", targetName, minutes);
        SendClientMessage(playerid, 0x00FF00FF, message);

        // Log admin command
        new logStr[256];
        format(logStr, sizeof(logStr), "ADMIN: %s offline jailed %s for %d minutes (Reason: %s)", adminName, targetName, minutes, reason);
        print(logStr);
    }
    else
    {
        new message[128];
        format(message, sizeof(message), "ERROR: Player %s tidak ditemukan di database!", targetName);
        SendClientMessage(playerid, 0xFF0000FF, message);
    }

    return 1;
}

// Callback: Offline warn
forward OnOfflineWarn(playerid, const targetName[], const adminName[], const reason[]);
public OnOfflineWarn(playerid, const targetName[], const adminName[], const reason[])
{
    new rows = cache_num_rows();

    if(rows > 0)
    {
        // Get current warn count from database
        new currentWarnCount;
        cache_get_value_int(0, "warn_count", currentWarnCount);

        // Increment warn count
        new newWarnCount = currentWarnCount + 1;

        // Update warn count in database
        new updateQuery[256];
        mysql_format(g_SQL, updateQuery, sizeof(updateQuery),
            "UPDATE `players` SET `warn_count` = %d WHERE `username` = '%e'",
            newWarnCount, targetName);
        mysql_tquery(g_SQL, updateQuery);

        // Add warn to database
        AddPlayerWarn(targetName, "WARN", adminName, reason);

        // Send messages
        new message[256];
        format(message, sizeof(message), "AdmCmd: %s (Offline) has been warned by %s, Total warn: %d", targetName, adminName, newWarnCount);
        SendClientMessageToAll(COLOR_ADMIN, message);

        format(message, sizeof(message), "Reason: %s", reason);
        SendClientMessageToAll(COLOR_ADMIN, message);

        // Confirmation to admin
        format(message, sizeof(message), "INFO: {FFFFFF}Player {FFFF00}%s {FFFFFF}berhasil di warn offline.", targetName);
        SendClientMessage(playerid, COLOR_KAKZAH, message);

        // Log admin command
        new logStr[256];
        format(logStr, sizeof(logStr), "ADMIN: %s offline warned %s (Reason: %s) - Total warns: %d", adminName, targetName, reason, newWarnCount);
        print(logStr);
    }
    else
    {
        new message[128];
        format(message, sizeof(message), "ERROR: Player %s tidak ditemukan di database!", targetName);
        SendClientMessage(playerid, 0xFF0000FF, message);
    }

    return 1;
}

// Callback: Offline ban
forward OnOfflineBan(playerid, const targetName[], const adminName[], const reason[], days);
public OnOfflineBan(playerid, const targetName[], const adminName[], const reason[], days)
{
    new affectedRows = cache_affected_rows();

    if(affectedRows > 0)
    {
        // Add ban to warns database
        AddPlayerWarn(targetName, "BAN", adminName, reason);

        // Send messages
        new message[256];
        if(days == 0)
        {
            format(message, sizeof(message), "AdmCmd: %s (Offline) has been banned by Admin %s", targetName, adminName);
        }
        else
        {
            format(message, sizeof(message), "AdmCmd: %s (Offline) has been banned by Admin %s for %d day(s)", targetName, adminName, days);
        }
        SendClientMessageToAll(COLOR_ADMIN, message);

        format(message, sizeof(message), "Reason: %s", reason);
        SendClientMessageToAll(COLOR_ADMIN, message);

        // Confirmation to admin
        if(days == 0)
        {
            format(message, sizeof(message), "INFO: {FFFFFF}Player {FFFF00}%s {FFFFFF}berhasil di ban offline secara permanent.", targetName);
        }
        else
        {
            format(message, sizeof(message), "INFO: {FFFFFF}Player {FFFF00}%s {FFFFFF}berhasil di ban offline selama {FFFF00}%d hari{FFFFFF}.", targetName, days);
        }
        SendClientMessage(playerid, COLOR_KAKZAH, message);

        // Log admin command
        new logStr[256];
        if(days == 0)
        {
            format(logStr, sizeof(logStr), "ADMIN: %s offline permanently banned %s (Reason: %s)", adminName, targetName, reason);
        }
        else
        {
            format(logStr, sizeof(logStr), "ADMIN: %s offline banned %s for %d days (Reason: %s)", adminName, targetName, days, reason);
        }
        print(logStr);
    }
    else
    {
        new message[128];
        format(message, sizeof(message), "ERROR: Player %s tidak ditemukan di database!", targetName);
        SendClientMessage(playerid, 0xFF0000FF, message);
    }

    return 1;
}

// Callback: Offline unban
forward OnOfflineUnban(playerid, const targetName[], const adminName[]);
public OnOfflineUnban(playerid, const targetName[], const adminName[])
{
    new affectedRows = cache_affected_rows();

    if(affectedRows > 0)
    {
        // Send messages
        new message[256];
        format(message, sizeof(message), "AdmCmd: %s (Offline) has been unbanned by Admin %s", targetName, adminName);
        SendClientMessageToAll(COLOR_ADMIN, message);

        // Confirmation to admin
        format(message, sizeof(message), "INFO: {FFFFFF}Player {FFFF00}%s {FFFFFF}berhasil di unban offline.", targetName);
        SendClientMessage(playerid, COLOR_KAKZAH, message);

        // Log admin command
        new logStr[256];
        format(logStr, sizeof(logStr), "ADMIN: %s offline unbanned %s", adminName, targetName);
        print(logStr);
    }
    else
    {
        new message[128];
        format(message, sizeof(message), "ERROR: Player %s tidak ditemukan di database atau tidak di ban!", targetName);
        SendClientMessage(playerid, 0xFF0000FF, message);
    }

    return 1;
}

// ========================================
// Body Part Status System
// ========================================

// Function to get body part name
stock GetBodyPartName(bodypart, output[])
{
    switch(bodypart)
    {
        case 3: strcpy(output, "Torso", 32);
        case 4: strcpy(output, "Groin", 32);
        case 5: strcpy(output, "Left arm", 32);
        case 6: strcpy(output, "Right arm", 32);
        case 7: strcpy(output, "Left leg", 32);
        case 8: strcpy(output, "Right leg", 32);
        case 9: strcpy(output, "Head", 32);
        default: strcpy(output, "Unknown", 32);
    }
    return 1;
}

// Function to get condition name
stock GetConditionName(condition, output[])
{
    switch(condition)
    {
        case CONDITION_NORMAL: strcpy(output, "Normal", 16);
        case CONDITION_DISLOCATED: strcpy(output, "Dislocated", 16);
        case CONDITION_BROKEN_BONE: strcpy(output, "Broken Bone", 16);
        case CONDITION_BRUISE: strcpy(output, "Bruise", 16);
        default: strcpy(output, "Unknown", 16);
    }
    return 1;
}

// Function to convert body part conditions to string for database
stock BodyPartConditionsToString(playerid, output[], maxlength = 32)
{
    new tempStr[8];
    output[0] = 0; // Clear string

    for(new i = 0; i < 7; i++)
    {
        format(tempStr, sizeof(tempStr), "%d", PlayerInfo[playerid][pBodyPartCondition][i]);
        strcat(output, tempStr, maxlength);
        if(i < 6) strcat(output, ",", maxlength);
    }
    return 1;
}

// Function to convert string to body part conditions from database
stock StringToBodyPartConditions(playerid, const input[])
{
    new field = 0;
    new len = strlen(input);
    new tempStr[8], tempIdx = 0;

    for(new i = 0; i <= len; i++)
    {
        if(input[i] == ',' || i == len)
        {
            tempStr[tempIdx] = 0;
            if(field < 7)
            {
                PlayerInfo[playerid][pBodyPartCondition][field] = strval(tempStr);
                field++;
            }
            tempIdx = 0;
        }
        else
        {
            tempStr[tempIdx] = input[i];
            tempIdx++;
        }
    }
    return 1;
}



// Command to show health status dialog (body parts + HBE)
CMD:myhealth(playerid, params[])
{
    if(!PlayerInfo[playerid][pLogged])
        return SendClientMessage(playerid, 0xFF0000FF, "ERROR: Anda harus login terlebih dahulu!");

    ShowHealthStatusDialog(playerid);
    return 1;
}

// Function to show health status dialog (body parts + HBE)
stock ShowHealthStatusDialog(playerid)
{
    new dialogStr[2048];
    new tempStr[256];
    new bodyPartName[32], conditionName[16];

    // Header
    strcat(dialogStr, "{FF00FF}====[Body part status]====\n");

    // Loop through body parts (3-9)
    new bodyPartIDs[7] = {3, 4, 5, 6, 7, 8, 9};
    for(new i = 0; i < 7; i++)
    {
        GetBodyPartName(bodyPartIDs[i], bodyPartName);
        GetConditionName(PlayerInfo[playerid][pBodyPartCondition][i], conditionName);

        // Format part name
        format(tempStr, sizeof(tempStr), "{FFFFFF}Part: {00FFFF}%s\n", bodyPartName);
        strcat(dialogStr, tempStr);

        // Format condition with direct color based on condition
        switch(PlayerInfo[playerid][pBodyPartCondition][i])
        {
            case CONDITION_NORMAL:
                format(tempStr, sizeof(tempStr), "{FFFFFF}Condition: {8AFF8A}%s\n", conditionName);
            case CONDITION_DISLOCATED:
                format(tempStr, sizeof(tempStr), "{FFFFFF}Condition: {FFFF00}%s\n", conditionName);
            case CONDITION_BROKEN_BONE:
                format(tempStr, sizeof(tempStr), "{FFFFFF}Condition: {FF0000}%s\n", conditionName);
            case CONDITION_BRUISE:
                format(tempStr, sizeof(tempStr), "{FFFFFF}Condition: {FFA500}%s\n", conditionName);
            default:
                format(tempStr, sizeof(tempStr), "{FFFFFF}Condition: {FFFFFF}%s\n", conditionName);
        }
        strcat(dialogStr, tempStr);
    }

    // Body Health section with HBE
    strcat(dialogStr, "{FF00FF}====[Body Health]====\n");

    // HBE Status
    new hungerColor = GetHBEColor(PlayerInfo[playerid][pHunger]);
    new bladderColor = GetHBEColor(PlayerInfo[playerid][pBladder]);
    new energyColor = GetHBEColor(PlayerInfo[playerid][pEnergy]);

    format(tempStr, sizeof(tempStr), "{FFFFFF}Hunger: {%06x}%d%%\n", hungerColor >>> 8, PlayerInfo[playerid][pHunger]);
    strcat(dialogStr, tempStr);

    format(tempStr, sizeof(tempStr), "{FFFFFF}Bladder: {%06x}%d%%\n", bladderColor >>> 8, PlayerInfo[playerid][pBladder]);
    strcat(dialogStr, tempStr);

    format(tempStr, sizeof(tempStr), "{FFFFFF}Energy: {%06x}%d%%\n", energyColor >>> 8, PlayerInfo[playerid][pEnergy]);
    strcat(dialogStr, tempStr);

    Dialog_Show(playerid, HealthStatus, DIALOG_STYLE_MSGBOX, "Character Health", dialogStr, "Close", "");
    return 1;
}

// Admin command to set body part condition (Admin Level 10+)
CMD:setbodypart(playerid, params[])
{
    if(!PlayerInfo[playerid][pLogged])
        return SendClientMessage(playerid, 0xFF0000FF, "ERROR: Anda belum login!");

    if(PlayerInfo[playerid][pAdminLevel] < 10)
        return SendClientMessage(playerid, 0xFF0000FF, "ERROR: Hanya admin level 10+ yang bisa menggunakan command ini!");

    new targetid, bodypart, condition;
    if(sscanf(params, "udd", targetid, bodypart, condition))
    {
        SendClientMessage(playerid, 0x808080FF, "USAGE: /setbodypart [playerid] [bodypart] [condition]");
        SendClientMessage(playerid, 0x808080FF, "Body Parts: 3=Torso, 4=Groin, 5=Left arm, 6=Right arm, 7=Left leg, 8=Right leg, 9=Head");
        SendClientMessage(playerid, 0x808080FF, "Conditions: 0=Normal, 1=Dislocated, 2=Broken Bone, 3=Bruise");
        return 1;
    }

    if(!IsPlayerConnected(targetid) || !PlayerInfo[targetid][pLogged])
        return SendClientMessage(playerid, 0xFF0000FF, "ERROR: Player tidak online atau belum login!");

    if(bodypart < 3 || bodypart > 9)
        return SendClientMessage(playerid, 0xFF0000FF, "ERROR: Body part tidak valid! (3-9)");

    if(condition < 0 || condition > 3)
        return SendClientMessage(playerid, 0xFF0000FF, "ERROR: Condition tidak valid! (0=Normal, 1=Dislocated, 2=Broken Bone, 3=Bruise)");

    // Convert bodypart ID to array index (3-9 becomes 0-6)
    new bodyPartIndex = bodypart - 3;

    // Set body part condition
    PlayerInfo[targetid][pBodyPartCondition][bodyPartIndex] = condition;

    // Get names for messages
    new targetName[MAX_PLAYER_NAME], adminName[32];
    new bodyPartName[32], conditionName[16];
    GetPlayerName(targetid, targetName, sizeof(targetName));
    GetPlayerAdminName(playerid, adminName, sizeof(adminName));
    GetBodyPartName(bodypart, bodyPartName);
    GetConditionName(condition, conditionName);

    // Send messages
    new message[128];
    format(message, sizeof(message), "AdmCmd: Admin %s mengubah kondisi %s Anda menjadi %s", adminName, bodyPartName, conditionName);
    SendClientMessage(targetid, COLOR_ADMIN, message);

    format(message, sizeof(message), "INFO: {FFFFFF}Anda mengubah kondisi %s %s menjadi %s", bodyPartName, targetName, conditionName);
    SendClientMessage(playerid, COLOR_KAKZAH, message);

    // Log admin command
    new logStr[256];
    format(logStr, sizeof(logStr), "ADMIN: %s set %s's %s condition to %s", adminName, targetName, bodyPartName, conditionName);
    print(logStr);

    return 1;
}

// Admin command to heal all body parts (Admin Level 10+)
CMD:healbodyparts(playerid, params[])
{
    if(!PlayerInfo[playerid][pLogged])
        return SendClientMessage(playerid, 0xFF0000FF, "ERROR: Anda belum login!");

    if(PlayerInfo[playerid][pAdminLevel] < 10)
        return SendClientMessage(playerid, 0xFF0000FF, "ERROR: Hanya admin level 10+ yang bisa menggunakan command ini!");

    new targetid;
    if(sscanf(params, "u", targetid))
        return SendClientMessage(playerid, 0x808080FF, "USAGE: /healbodyparts [playerid]");

    if(!IsPlayerConnected(targetid) || !PlayerInfo[targetid][pLogged])
        return SendClientMessage(playerid, 0xFF0000FF, "ERROR: Player tidak online atau belum login!");

    // Heal all body parts
    for(new i = 0; i < 7; i++)
    {
        PlayerInfo[targetid][pBodyPartCondition][i] = CONDITION_NORMAL;
    }

    // Get names for messages
    new targetName[MAX_PLAYER_NAME], adminName[32];
    GetPlayerName(targetid, targetName, sizeof(targetName));
    GetPlayerAdminName(playerid, adminName, sizeof(adminName));

    // Send messages
    new message[128];
    format(message, sizeof(message), "AdmCmd: Admin %s telah menyembuhkan semua body parts Anda", adminName);
    SendClientMessage(targetid, COLOR_ADMIN, message);

    format(message, sizeof(message), "INFO: {FFFFFF}Anda telah menyembuhkan semua body parts %s", targetName);
    SendClientMessage(playerid, COLOR_KAKZAH, message);

    // Log admin command
    new logStr[256];
    format(logStr, sizeof(logStr), "ADMIN: %s healed all body parts for %s", adminName, targetName);
    print(logStr);

    return 1;
}

// ========================================
// HBE (Hunger, Bladder, Energy) System
// ========================================

// Function to create HBE textdraws and progress bars
stock CreateHBETextDraw(playerid)
{
    if(PlayerInfo[playerid][pHBEMode] == 0)
    {
        CreateClassicHBETextDraw(playerid);
    }
    else
    {
        CreateSimpleHBETextDraw(playerid);
    }
}

// Function to create Classic HBE textdraws and progress bars
stock CreateClassicHBETextDraw(playerid)
{
    // Background box
    PlayerTD[playerid][0] = CreatePlayerTextDraw(playerid, -72.000000, 252.000000, "_");
    PlayerTextDrawFont(playerid, PlayerTD[playerid][0], 1);
    PlayerTextDrawLetterSize(playerid, PlayerTD[playerid][0], 0.550000, 7.949997);
    PlayerTextDrawTextSize(playerid, PlayerTD[playerid][0], 61.500000, 16.500000);
    PlayerTextDrawSetOutline(playerid, PlayerTD[playerid][0], 1);
    PlayerTextDrawSetShadow(playerid, PlayerTD[playerid][0], 0);
    PlayerTextDrawAlignment(playerid, PlayerTD[playerid][0], 1);
    PlayerTextDrawColor(playerid, PlayerTD[playerid][0], -1);
    PlayerTextDrawBackgroundColor(playerid, PlayerTD[playerid][0], 255);
    PlayerTextDrawBoxColor(playerid, PlayerTD[playerid][0], 225);
    PlayerTextDrawUseBox(playerid, PlayerTD[playerid][0], 1);
    PlayerTextDrawSetProportional(playerid, PlayerTD[playerid][0], 1);
    PlayerTextDrawSetSelectable(playerid, PlayerTD[playerid][0], 0);

    // Hunger background
    PlayerTD[playerid][1] = CreatePlayerTextDraw(playerid, 3.000000, 243.000000, ".");
    PlayerTextDrawFont(playerid, PlayerTD[playerid][1], 1);
    PlayerTextDrawLetterSize(playerid, PlayerTD[playerid][1], 0.524999, 2.349998);
    PlayerTextDrawTextSize(playerid, PlayerTD[playerid][1], 400.000000, 17.000000);
    PlayerTextDrawSetOutline(playerid, PlayerTD[playerid][1], 1);
    PlayerTextDrawSetShadow(playerid, PlayerTD[playerid][1], 0);
    PlayerTextDrawAlignment(playerid, PlayerTD[playerid][1], 1);
    PlayerTextDrawColor(playerid, PlayerTD[playerid][1], -1);
    PlayerTextDrawBackgroundColor(playerid, PlayerTD[playerid][1], 255);
    PlayerTextDrawBoxColor(playerid, PlayerTD[playerid][1], 50);
    PlayerTextDrawUseBox(playerid, PlayerTD[playerid][1], 0);
    PlayerTextDrawSetProportional(playerid, PlayerTD[playerid][1], 1);
    PlayerTextDrawSetSelectable(playerid, PlayerTD[playerid][1], 0);

    // Bladder background
    PlayerTD[playerid][2] = CreatePlayerTextDraw(playerid, 3.000000, 265.000000, ".");
    PlayerTextDrawFont(playerid, PlayerTD[playerid][2], 1);
    PlayerTextDrawLetterSize(playerid, PlayerTD[playerid][2], 0.524999, 2.349998);
    PlayerTextDrawTextSize(playerid, PlayerTD[playerid][2], 400.000000, 17.000000);
    PlayerTextDrawSetOutline(playerid, PlayerTD[playerid][2], 1);
    PlayerTextDrawSetShadow(playerid, PlayerTD[playerid][2], 0);
    PlayerTextDrawAlignment(playerid, PlayerTD[playerid][2], 1);
    PlayerTextDrawColor(playerid, PlayerTD[playerid][2], -1);
    PlayerTextDrawBackgroundColor(playerid, PlayerTD[playerid][2], 255);
    PlayerTextDrawBoxColor(playerid, PlayerTD[playerid][2], 50);
    PlayerTextDrawUseBox(playerid, PlayerTD[playerid][2], 0);
    PlayerTextDrawSetProportional(playerid, PlayerTD[playerid][2], 1);
    PlayerTextDrawSetSelectable(playerid, PlayerTD[playerid][2], 0);

    // Energy background
    PlayerTD[playerid][3] = CreatePlayerTextDraw(playerid, 3.000000, 287.000000, ".");
    PlayerTextDrawFont(playerid, PlayerTD[playerid][3], 1);
    PlayerTextDrawLetterSize(playerid, PlayerTD[playerid][3], 0.524999, 2.349998);
    PlayerTextDrawTextSize(playerid, PlayerTD[playerid][3], 400.000000, 17.000000);
    PlayerTextDrawSetOutline(playerid, PlayerTD[playerid][3], 1);
    PlayerTextDrawSetShadow(playerid, PlayerTD[playerid][3], 0);
    PlayerTextDrawAlignment(playerid, PlayerTD[playerid][3], 1);
    PlayerTextDrawColor(playerid, PlayerTD[playerid][3], -1);
    PlayerTextDrawBackgroundColor(playerid, PlayerTD[playerid][3], 255);
    PlayerTextDrawBoxColor(playerid, PlayerTD[playerid][3], 50);
    PlayerTextDrawUseBox(playerid, PlayerTD[playerid][3], 0);
    PlayerTextDrawSetProportional(playerid, PlayerTD[playerid][3], 1);
    PlayerTextDrawSetSelectable(playerid, PlayerTD[playerid][3], 0);

    // Hunger label
    PlayerTD[playerid][4] = CreatePlayerTextDraw(playerid, 13.000000, 253.000000, "Hunger:");
    PlayerTextDrawFont(playerid, PlayerTD[playerid][4], 1);
    PlayerTextDrawLetterSize(playerid, PlayerTD[playerid][4], 0.220833, 1.100000);
    PlayerTextDrawTextSize(playerid, PlayerTD[playerid][4], 400.000000, 17.000000);
    PlayerTextDrawSetOutline(playerid, PlayerTD[playerid][4], 1);
    PlayerTextDrawSetShadow(playerid, PlayerTD[playerid][4], 0);
    PlayerTextDrawAlignment(playerid, PlayerTD[playerid][4], 1);
    PlayerTextDrawColor(playerid, PlayerTD[playerid][4], -1);
    PlayerTextDrawBackgroundColor(playerid, PlayerTD[playerid][4], 255);
    PlayerTextDrawBoxColor(playerid, PlayerTD[playerid][4], 50);
    PlayerTextDrawUseBox(playerid, PlayerTD[playerid][4], 0);
    PlayerTextDrawSetProportional(playerid, PlayerTD[playerid][4], 1);
    PlayerTextDrawSetSelectable(playerid, PlayerTD[playerid][4], 0);

    // Bladder label
    PlayerTD[playerid][5] = CreatePlayerTextDraw(playerid, 13.000000, 275.000000, "Bladder:");
    PlayerTextDrawFont(playerid, PlayerTD[playerid][5], 1);
    PlayerTextDrawLetterSize(playerid, PlayerTD[playerid][5], 0.220833, 1.100000);
    PlayerTextDrawTextSize(playerid, PlayerTD[playerid][5], 400.000000, 17.000000);
    PlayerTextDrawSetOutline(playerid, PlayerTD[playerid][5], 1);
    PlayerTextDrawSetShadow(playerid, PlayerTD[playerid][5], 0);
    PlayerTextDrawAlignment(playerid, PlayerTD[playerid][5], 1);
    PlayerTextDrawColor(playerid, PlayerTD[playerid][5], -1);
    PlayerTextDrawBackgroundColor(playerid, PlayerTD[playerid][5], 255);
    PlayerTextDrawBoxColor(playerid, PlayerTD[playerid][5], 50);
    PlayerTextDrawUseBox(playerid, PlayerTD[playerid][5], 0);
    PlayerTextDrawSetProportional(playerid, PlayerTD[playerid][5], 1);
    PlayerTextDrawSetSelectable(playerid, PlayerTD[playerid][5], 0);

    // Energy label
    PlayerTD[playerid][6] = CreatePlayerTextDraw(playerid, 13.000000, 297.000000, "Energy:");
    PlayerTextDrawFont(playerid, PlayerTD[playerid][6], 1);
    PlayerTextDrawLetterSize(playerid, PlayerTD[playerid][6], 0.220833, 1.100000);
    PlayerTextDrawTextSize(playerid, PlayerTD[playerid][6], 400.000000, 17.000000);
    PlayerTextDrawSetOutline(playerid, PlayerTD[playerid][6], 1);
    PlayerTextDrawSetShadow(playerid, PlayerTD[playerid][6], 0);
    PlayerTextDrawAlignment(playerid, PlayerTD[playerid][6], 1);
    PlayerTextDrawColor(playerid, PlayerTD[playerid][6], -1);
    PlayerTextDrawBackgroundColor(playerid, PlayerTD[playerid][6], 255);
    PlayerTextDrawBoxColor(playerid, PlayerTD[playerid][6], 50);
    PlayerTextDrawUseBox(playerid, PlayerTD[playerid][6], 0);
    PlayerTextDrawSetProportional(playerid, PlayerTD[playerid][6], 1);
    PlayerTextDrawSetSelectable(playerid, PlayerTD[playerid][6], 0);

    // Create progress bars
    PlayerProgressBar[playerid][0] = CreatePlayerProgressBar(playerid, 14.000000, 267.000000, 45.000000, 4.000000, 0x8AFF8AFF, 100.000000, BAR_DIRECTION_RIGHT);
    SetPlayerProgressBarValue(playerid, PlayerProgressBar[playerid][0], float(PlayerInfo[playerid][pHunger]));

    PlayerProgressBar[playerid][1] = CreatePlayerProgressBar(playerid, 14.000000, 288.000000, 45.000000, 4.000000, 0x8AFF8AFF, 100.000000, BAR_DIRECTION_RIGHT);
    SetPlayerProgressBarValue(playerid, PlayerProgressBar[playerid][1], float(PlayerInfo[playerid][pBladder]));

    PlayerProgressBar[playerid][2] = CreatePlayerProgressBar(playerid, 14.000000, 311.000000, 45.000000, 4.000000, 0x8AFF8AFF, 100.000000, BAR_DIRECTION_RIGHT);
    SetPlayerProgressBarValue(playerid, PlayerProgressBar[playerid][2], float(PlayerInfo[playerid][pEnergy]));

    // Show all textdraws and progress bars
    for(new i = 0; i < 7; i++)
    {
        PlayerTextDrawShow(playerid, PlayerTD[playerid][i]);
    }
    for(new i = 0; i < 3; i++)
    {
        ShowPlayerProgressBar(playerid, PlayerProgressBar[playerid][i]);
    }

    // Update progress bar colors based on current values
    UpdateHBEProgressBars(playerid);
}

// Function to create Simple HBE textdraws (no progress bars)
stock CreateSimpleHBETextDraw(playerid)
{
    // Background box
    PlayerTD[playerid][0] = CreatePlayerTextDraw(playerid, 1.000000, 270.000000, "_");
    PlayerTextDrawFont(playerid, PlayerTD[playerid][0], 0);
    PlayerTextDrawLetterSize(playerid, PlayerTD[playerid][0], 0.741666, 6.249995);
    PlayerTextDrawTextSize(playerid, PlayerTD[playerid][0], 47.500000, 30.500000);
    PlayerTextDrawSetOutline(playerid, PlayerTD[playerid][0], 1);
    PlayerTextDrawSetShadow(playerid, PlayerTD[playerid][0], 0);
    PlayerTextDrawAlignment(playerid, PlayerTD[playerid][0], 1);
    PlayerTextDrawColor(playerid, PlayerTD[playerid][0], -1);
    PlayerTextDrawBackgroundColor(playerid, PlayerTD[playerid][0], 255);
    PlayerTextDrawBoxColor(playerid, PlayerTD[playerid][0], 225);
    PlayerTextDrawUseBox(playerid, PlayerTD[playerid][0], 1);
    PlayerTextDrawSetProportional(playerid, PlayerTD[playerid][0], 1);
    PlayerTextDrawSetSelectable(playerid, PlayerTD[playerid][0], 0);

    // Hunger icon
    PlayerTD[playerid][1] = CreatePlayerTextDraw(playerid, 0.000000, 273.000000, "HUD:radar_datefood");
    PlayerTextDrawFont(playerid, PlayerTD[playerid][1], 4);
    PlayerTextDrawLetterSize(playerid, PlayerTD[playerid][1], 0.600000, 2.000000);
    PlayerTextDrawTextSize(playerid, PlayerTD[playerid][1], 15.000000, 13.000000);
    PlayerTextDrawSetOutline(playerid, PlayerTD[playerid][1], 1);
    PlayerTextDrawSetShadow(playerid, PlayerTD[playerid][1], 0);
    PlayerTextDrawAlignment(playerid, PlayerTD[playerid][1], 1);
    PlayerTextDrawColor(playerid, PlayerTD[playerid][1], -1);
    PlayerTextDrawBackgroundColor(playerid, PlayerTD[playerid][1], 255);
    PlayerTextDrawBoxColor(playerid, PlayerTD[playerid][1], 50);
    PlayerTextDrawUseBox(playerid, PlayerTD[playerid][1], 0);
    PlayerTextDrawSetProportional(playerid, PlayerTD[playerid][1], 1);
    PlayerTextDrawSetSelectable(playerid, PlayerTD[playerid][1], 0);

    // Hunger value
    PlayerTD[playerid][2] = CreatePlayerTextDraw(playerid, 25.000000, 276.000000, "100");
    PlayerTextDrawFont(playerid, PlayerTD[playerid][2], 1);
    PlayerTextDrawLetterSize(playerid, PlayerTD[playerid][2], 0.245832, 1.200000);
    PlayerTextDrawTextSize(playerid, PlayerTD[playerid][2], 400.000000, 17.000000);
    PlayerTextDrawSetOutline(playerid, PlayerTD[playerid][2], 1);
    PlayerTextDrawSetShadow(playerid, PlayerTD[playerid][2], 0);
    PlayerTextDrawAlignment(playerid, PlayerTD[playerid][2], 1);
    PlayerTextDrawColor(playerid, PlayerTD[playerid][2], -1);
    PlayerTextDrawBackgroundColor(playerid, PlayerTD[playerid][2], 255);
    PlayerTextDrawBoxColor(playerid, PlayerTD[playerid][2], 50);
    PlayerTextDrawUseBox(playerid, PlayerTD[playerid][2], 0);
    PlayerTextDrawSetProportional(playerid, PlayerTD[playerid][2], 1);
    PlayerTextDrawSetSelectable(playerid, PlayerTD[playerid][2], 0);

    // Hunger separator
    PlayerTD[playerid][3] = CreatePlayerTextDraw(playerid, 20.000000, 276.000000, ":");
    PlayerTextDrawFont(playerid, PlayerTD[playerid][3], 1);
    PlayerTextDrawLetterSize(playerid, PlayerTD[playerid][3], 0.245832, 1.200000);
    PlayerTextDrawTextSize(playerid, PlayerTD[playerid][3], 400.000000, 17.000000);
    PlayerTextDrawSetOutline(playerid, PlayerTD[playerid][3], 1);
    PlayerTextDrawSetShadow(playerid, PlayerTD[playerid][3], 0);
    PlayerTextDrawAlignment(playerid, PlayerTD[playerid][3], 2);
    PlayerTextDrawColor(playerid, PlayerTD[playerid][3], -1);
    PlayerTextDrawBackgroundColor(playerid, PlayerTD[playerid][3], 255);
    PlayerTextDrawBoxColor(playerid, PlayerTD[playerid][3], 50);
    PlayerTextDrawUseBox(playerid, PlayerTD[playerid][3], 0);
    PlayerTextDrawSetProportional(playerid, PlayerTD[playerid][3], 1);
    PlayerTextDrawSetSelectable(playerid, PlayerTD[playerid][3], 0);

    // Bladder icon (toilet)
    PlayerTD[playerid][4] = CreatePlayerTextDraw(playerid, -6.000000, 290.000000, "vehicle:vehicleenvmap128");
    PlayerTextDrawFont(playerid, PlayerTD[playerid][4], 5);
    PlayerTextDrawLetterSize(playerid, PlayerTD[playerid][4], 0.600000, 2.000000);
    PlayerTextDrawTextSize(playerid, PlayerTD[playerid][4], 20.500000, 13.500000);
    PlayerTextDrawSetOutline(playerid, PlayerTD[playerid][4], 1);
    PlayerTextDrawSetShadow(playerid, PlayerTD[playerid][4], 0);
    PlayerTextDrawAlignment(playerid, PlayerTD[playerid][4], 1);
    PlayerTextDrawColor(playerid, PlayerTD[playerid][4], -1);
    PlayerTextDrawBackgroundColor(playerid, PlayerTD[playerid][4], 0);
    PlayerTextDrawBoxColor(playerid, PlayerTD[playerid][4], 0);
    PlayerTextDrawUseBox(playerid, PlayerTD[playerid][4], 0);
    PlayerTextDrawSetProportional(playerid, PlayerTD[playerid][4], 1);
    PlayerTextDrawSetSelectable(playerid, PlayerTD[playerid][4], 0);
    PlayerTextDrawSetPreviewModel(playerid, PlayerTD[playerid][4], 2525);
    PlayerTextDrawSetPreviewRot(playerid, PlayerTD[playerid][4], -10.000000, 0.000000, 31.000000, 0.899999);
    PlayerTextDrawSetPreviewVehCol(playerid, PlayerTD[playerid][4], 1, 1);

    // Energy icon
    PlayerTD[playerid][5] = CreatePlayerTextDraw(playerid, 0.000000, 308.000000, "HUD:radar_diner");
    PlayerTextDrawFont(playerid, PlayerTD[playerid][5], 4);
    PlayerTextDrawLetterSize(playerid, PlayerTD[playerid][5], 0.600000, 2.000000);
    PlayerTextDrawTextSize(playerid, PlayerTD[playerid][5], 15.000000, 13.000000);
    PlayerTextDrawSetOutline(playerid, PlayerTD[playerid][5], 1);
    PlayerTextDrawSetShadow(playerid, PlayerTD[playerid][5], 0);
    PlayerTextDrawAlignment(playerid, PlayerTD[playerid][5], 1);
    PlayerTextDrawColor(playerid, PlayerTD[playerid][5], -1);
    PlayerTextDrawBackgroundColor(playerid, PlayerTD[playerid][5], 255);
    PlayerTextDrawBoxColor(playerid, PlayerTD[playerid][5], 50);
    PlayerTextDrawUseBox(playerid, PlayerTD[playerid][5], 0);
    PlayerTextDrawSetProportional(playerid, PlayerTD[playerid][5], 1);
    PlayerTextDrawSetSelectable(playerid, PlayerTD[playerid][5], 0);

    // Bladder separator
    PlayerTD[playerid][6] = CreatePlayerTextDraw(playerid, 20.000000, 294.000000, ":");
    PlayerTextDrawFont(playerid, PlayerTD[playerid][6], 1);
    PlayerTextDrawLetterSize(playerid, PlayerTD[playerid][6], 0.245832, 1.200000);
    PlayerTextDrawTextSize(playerid, PlayerTD[playerid][6], 400.000000, 17.000000);
    PlayerTextDrawSetOutline(playerid, PlayerTD[playerid][6], 1);
    PlayerTextDrawSetShadow(playerid, PlayerTD[playerid][6], 0);
    PlayerTextDrawAlignment(playerid, PlayerTD[playerid][6], 2);
    PlayerTextDrawColor(playerid, PlayerTD[playerid][6], -1);
    PlayerTextDrawBackgroundColor(playerid, PlayerTD[playerid][6], 255);
    PlayerTextDrawBoxColor(playerid, PlayerTD[playerid][6], 50);
    PlayerTextDrawUseBox(playerid, PlayerTD[playerid][6], 0);
    PlayerTextDrawSetProportional(playerid, PlayerTD[playerid][6], 1);
    PlayerTextDrawSetSelectable(playerid, PlayerTD[playerid][6], 0);

    // Energy separator
    PlayerTD[playerid][7] = CreatePlayerTextDraw(playerid, 20.000000, 311.000000, ":");
    PlayerTextDrawFont(playerid, PlayerTD[playerid][7], 1);
    PlayerTextDrawLetterSize(playerid, PlayerTD[playerid][7], 0.245832, 1.200000);
    PlayerTextDrawTextSize(playerid, PlayerTD[playerid][7], 400.000000, 17.000000);
    PlayerTextDrawSetOutline(playerid, PlayerTD[playerid][7], 1);
    PlayerTextDrawSetShadow(playerid, PlayerTD[playerid][7], 0);
    PlayerTextDrawAlignment(playerid, PlayerTD[playerid][7], 2);
    PlayerTextDrawColor(playerid, PlayerTD[playerid][7], -1);
    PlayerTextDrawBackgroundColor(playerid, PlayerTD[playerid][7], 255);
    PlayerTextDrawBoxColor(playerid, PlayerTD[playerid][7], 50);
    PlayerTextDrawUseBox(playerid, PlayerTD[playerid][7], 0);
    PlayerTextDrawSetProportional(playerid, PlayerTD[playerid][7], 1);
    PlayerTextDrawSetSelectable(playerid, PlayerTD[playerid][7], 0);

    // Bladder value
    PlayerTD[playerid][8] = CreatePlayerTextDraw(playerid, 25.000000, 294.000000, "100");
    PlayerTextDrawFont(playerid, PlayerTD[playerid][8], 1);
    PlayerTextDrawLetterSize(playerid, PlayerTD[playerid][8], 0.245832, 1.200000);
    PlayerTextDrawTextSize(playerid, PlayerTD[playerid][8], 400.000000, 17.000000);
    PlayerTextDrawSetOutline(playerid, PlayerTD[playerid][8], 1);
    PlayerTextDrawSetShadow(playerid, PlayerTD[playerid][8], 0);
    PlayerTextDrawAlignment(playerid, PlayerTD[playerid][8], 1);
    PlayerTextDrawColor(playerid, PlayerTD[playerid][8], -1);
    PlayerTextDrawBackgroundColor(playerid, PlayerTD[playerid][8], 255);
    PlayerTextDrawBoxColor(playerid, PlayerTD[playerid][8], 50);
    PlayerTextDrawUseBox(playerid, PlayerTD[playerid][8], 0);
    PlayerTextDrawSetProportional(playerid, PlayerTD[playerid][8], 1);
    PlayerTextDrawSetSelectable(playerid, PlayerTD[playerid][8], 0);

    // Energy value
    PlayerTD[playerid][9] = CreatePlayerTextDraw(playerid, 25.000000, 311.000000, "100");
    PlayerTextDrawFont(playerid, PlayerTD[playerid][9], 1);
    PlayerTextDrawLetterSize(playerid, PlayerTD[playerid][9], 0.245832, 1.200000);
    PlayerTextDrawTextSize(playerid, PlayerTD[playerid][9], 400.000000, 17.000000);
    PlayerTextDrawSetOutline(playerid, PlayerTD[playerid][9], 1);
    PlayerTextDrawSetShadow(playerid, PlayerTD[playerid][9], 0);
    PlayerTextDrawAlignment(playerid, PlayerTD[playerid][9], 1);
    PlayerTextDrawColor(playerid, PlayerTD[playerid][9], -1);
    PlayerTextDrawBackgroundColor(playerid, PlayerTD[playerid][9], 255);
    PlayerTextDrawBoxColor(playerid, PlayerTD[playerid][9], 50);
    PlayerTextDrawUseBox(playerid, PlayerTD[playerid][9], 0);
    PlayerTextDrawSetProportional(playerid, PlayerTD[playerid][9], 1);
    PlayerTextDrawSetSelectable(playerid, PlayerTD[playerid][9], 0);

    // Show all textdraws (no progress bars in simple mode)
    for(new i = 0; i < 10; i++)
    {
        PlayerTextDrawShow(playerid, PlayerTD[playerid][i]);
    }

    // Update text values and colors
    UpdateSimpleHBETextDraw(playerid);
}

// Function to destroy HBE textdraws and progress bars
stock DestroyHBETextDraw(playerid)
{
    for(new i = 0; i < 10; i++)
    {
        if(PlayerTD[playerid][i] != PlayerText:INVALID_TEXT_DRAW)
        {
            PlayerTextDrawHide(playerid, PlayerTD[playerid][i]);
            PlayerTextDrawDestroy(playerid, PlayerTD[playerid][i]);
            PlayerTD[playerid][i] = PlayerText:INVALID_TEXT_DRAW;
        }
    }
    for(new i = 0; i < 3; i++)
    {
        if(PlayerProgressBar[playerid][i] != PlayerBar:INVALID_PLAYER_BAR_ID)
        {
            HidePlayerProgressBar(playerid, PlayerProgressBar[playerid][i]);
            DestroyPlayerProgressBar(playerid, PlayerProgressBar[playerid][i]);
            PlayerProgressBar[playerid][i] = PlayerBar:INVALID_PLAYER_BAR_ID;
        }
    }
}

// Function to get progress bar color based on value
stock GetHBEColor(value)
{
    if(value >= 100) return 0x00FF00FF;      // Green (100%)
    else if(value >= 80) return 0x8AFF8AFF;  // Light Green (80-99%)
    else if(value >= 60) return 0xFFA500FF;  // Orange (60-79%)
    else if(value >= 40) return 0xFF8C00FF;  // Dark Orange (40-59%)
    else if(value >= 30) return 0xFF0000FF;  // Red (30-39%)
    else return 0x8B0000FF;                  // Dark Red (0-29%)
}

// Function to update HBE progress bars colors and values
public UpdateHBEProgressBars(playerid)
{
    // Safety check for valid playerid
    if(playerid < 0 || playerid >= MAX_PLAYERS) return 0;

    if(!IsPlayerConnected(playerid) || !PlayerInfo[playerid][pLogged]) return 0;

    if(PlayerInfo[playerid][pHBEMode] == 0) // Classic mode
    {
        // Update hunger progress bar
        if(PlayerProgressBar[playerid][0] != PlayerBar:INVALID_PLAYER_BAR_ID)
        {
            SetPlayerProgressBarValue(playerid, PlayerProgressBar[playerid][0], float(PlayerInfo[playerid][pHunger]));
            SetPlayerProgressBarColour(playerid, PlayerProgressBar[playerid][0], GetHBEColor(PlayerInfo[playerid][pHunger]));
        }

        // Update bladder progress bar
        if(PlayerProgressBar[playerid][1] != PlayerBar:INVALID_PLAYER_BAR_ID)
        {
            SetPlayerProgressBarValue(playerid, PlayerProgressBar[playerid][1], float(PlayerInfo[playerid][pBladder]));
            SetPlayerProgressBarColour(playerid, PlayerProgressBar[playerid][1], GetHBEColor(PlayerInfo[playerid][pBladder]));
        }

        // Update energy progress bar
        if(PlayerProgressBar[playerid][2] != PlayerBar:INVALID_PLAYER_BAR_ID)
        {
            SetPlayerProgressBarValue(playerid, PlayerProgressBar[playerid][2], float(PlayerInfo[playerid][pEnergy]));
            SetPlayerProgressBarColour(playerid, PlayerProgressBar[playerid][2], GetHBEColor(PlayerInfo[playerid][pEnergy]));
        }
    }
    else // Simple mode
    {
        UpdateSimpleHBETextDraw(playerid);
    }

    return 1;
}

// Function to update Simple HBE textdraw values and colors
stock UpdateSimpleHBETextDraw(playerid)
{
    // Safety check for valid playerid
    if(playerid < 0 || playerid >= MAX_PLAYERS) return 0;

    if(!IsPlayerConnected(playerid) || !PlayerInfo[playerid][pLogged]) return 0;

    new string[16];

    // Update hunger value and color
    if(PlayerTD[playerid][2] != PlayerText:INVALID_TEXT_DRAW)
    {
        format(string, sizeof(string), "%d", PlayerInfo[playerid][pHunger]);
        PlayerTextDrawSetString(playerid, PlayerTD[playerid][2], string);
        PlayerTextDrawColor(playerid, PlayerTD[playerid][2], GetHBEColor(PlayerInfo[playerid][pHunger]));
        PlayerTextDrawShow(playerid, PlayerTD[playerid][2]);
    }

    // Update bladder value and color
    if(PlayerTD[playerid][8] != PlayerText:INVALID_TEXT_DRAW)
    {
        format(string, sizeof(string), "%d", PlayerInfo[playerid][pBladder]);
        PlayerTextDrawSetString(playerid, PlayerTD[playerid][8], string);
        PlayerTextDrawColor(playerid, PlayerTD[playerid][8], GetHBEColor(PlayerInfo[playerid][pBladder]));
        PlayerTextDrawShow(playerid, PlayerTD[playerid][8]);
    }

    // Update energy value and color
    if(PlayerTD[playerid][9] != PlayerText:INVALID_TEXT_DRAW)
    {
        format(string, sizeof(string), "%d", PlayerInfo[playerid][pEnergy]);
        PlayerTextDrawSetString(playerid, PlayerTD[playerid][9], string);
        PlayerTextDrawColor(playerid, PlayerTD[playerid][9], GetHBEColor(PlayerInfo[playerid][pEnergy]));
        PlayerTextDrawShow(playerid, PlayerTD[playerid][9]);
    }

    return 1;
}

// Main HBE system timer - decreases values every minute
public UpdateHBESystem(playerid)
{
    // Safety check for valid playerid
    if(playerid < 0 || playerid >= MAX_PLAYERS) return 0;

    if(!IsPlayerConnected(playerid) || !PlayerInfo[playerid][pLogged]) return 0;

    // Don't decrease if player is dead or jailed
    if(PlayerInfo[playerid][pIsDead] || PlayerInfo[playerid][pJailed]) return 1;

    // Decrease hunger by 1% every minute
    if(PlayerInfo[playerid][pHunger] > 0)
    {
        PlayerInfo[playerid][pHunger]--;
    }

    // Decrease bladder by 1% every minute
    if(PlayerInfo[playerid][pBladder] > 0)
    {
        PlayerInfo[playerid][pBladder]--;
    }

    // Decrease energy by 1% every minute
    if(PlayerInfo[playerid][pEnergy] > 0)
    {
        PlayerInfo[playerid][pEnergy]--;
    }

    // Update progress bars
    UpdateHBEProgressBars(playerid);

    // Check for critical levels and send warnings
    if(PlayerInfo[playerid][pHunger] <= 10)
    {
        SendClientMessage(playerid, 0xFF0000FF, "WARNING: {FFFFFF}Hunger level sangat rendah! Segera cari makanan!");
    }
    if(PlayerInfo[playerid][pBladder] <= 10)
    {
        SendClientMessage(playerid, 0xFF0000FF, "WARNING: {FFFFFF}Bladder level sangat rendah! Segera ke toilet!");
    }
    if(PlayerInfo[playerid][pEnergy] <= 10)
    {
        SendClientMessage(playerid, 0xFF0000FF, "WARNING: {FFFFFF}Energy level sangat rendah! Segera istirahat!");
    }

    return 1;
}

// Pissing timer - completes pissing based on bladder level (5-60 seconds)
public OnPissTimerUpdate(playerid)
{
    // Safety check for valid playerid
    if(playerid < 0 || playerid >= MAX_PLAYERS) return 0;

    if(!IsPlayerConnected(playerid) || !PlayerInfo[playerid][pLogged] || !PlayerInfo[playerid][pIsPissing])
    {
        // Stop pissing if conditions not met
        if(PlayerInfo[playerid][pPissTimer] != 0)
        {
            KillTimer(PlayerInfo[playerid][pPissTimer]);
            PlayerInfo[playerid][pPissTimer] = 0;
        }
        PlayerInfo[playerid][pIsPissing] = false;
        if(IsPlayerConnected(playerid))
        {
            SetPlayerSpecialAction(playerid, SPECIAL_ACTION_NONE);
            ClearAnimations(playerid);
        }
        return 0;
    }

    // After 1 minute, complete pissing process
    PlayerInfo[playerid][pIsPissing] = false;
    if(PlayerInfo[playerid][pPissTimer] != 0)
    {
        KillTimer(PlayerInfo[playerid][pPissTimer]);
        PlayerInfo[playerid][pPissTimer] = 0;
    }

    // Set bladder to 100%
    PlayerInfo[playerid][pBladder] = 100;
    UpdateHBEProgressBars(playerid);

    // Stop animation
    SetPlayerSpecialAction(playerid, SPECIAL_ACTION_NONE);
    ClearAnimations(playerid);

    SendClientMessage(playerid, COLOR_KAKZAH, "SERVER: {FFFFFF}Kencing selesai. Bladder sudah penuh kembali.");

    return 1;
}

// Command /piss - untuk kencing
CMD:piss(playerid, params[])
{
    if(!PlayerInfo[playerid][pLogged])
    {
        SendClientMessage(playerid, 0xFF0000FF, "ERROR: Anda harus login terlebih dahulu!");
        return 1;
    }

    if(PlayerInfo[playerid][pIsDead])
    {
        SendClientMessage(playerid, 0xFF0000FF, "ERROR: Anda tidak bisa melakukan ini saat mati!");
        return 1;
    }

    if(PlayerInfo[playerid][pJailed])
    {
        SendClientMessage(playerid, 0xFF0000FF, "ERROR: Anda tidak bisa melakukan ini saat di jail!");
        return 1;
    }

    if(PlayerInfo[playerid][pIsPissing])
    {
        SendClientMessage(playerid, 0xFF0000FF, "ERROR: Anda sudah sedang kencing!");
        return 1;
    }

    if(PlayerInfo[playerid][pBladder] >= 100)
    {
        SendClientMessage(playerid, 0xFF0000FF, "ERROR: Bladder Anda sudah penuh!");
        return 1;
    }

    // Stop any existing piss timer first
    if(PlayerInfo[playerid][pPissTimer] != 0)
    {
        KillTimer(PlayerInfo[playerid][pPissTimer]);
        PlayerInfo[playerid][pPissTimer] = 0;
    }

    // Start pissing
    PlayerInfo[playerid][pIsPissing] = true;

    // Apply pissing special action
    SetPlayerSpecialAction(playerid, SPECIAL_ACTION_PISSING);

    // Calculate pissing duration based on bladder level
    new durationSeconds = GetPissingDuration(PlayerInfo[playerid][pBladder]);
    new pissDuration = durationSeconds * 1000; // Convert to milliseconds

    // Start piss timer with calculated duration
    new funcname[] = "OnPissTimerUpdate";
    new formatstr[] = "i";
    PlayerInfo[playerid][pPissTimer] = SetTimerEx(funcname, pissDuration, false, formatstr, playerid);

    // Inform player about duration
    new string[128];
    format(string, sizeof(string), "SERVER: {FFFFFF}Anda mulai kencing. Proses akan selesai dalam {FFFF00}%d detik{FFFFFF}.", durationSeconds);
    SendClientMessage(playerid, COLOR_KAKZAH, string);

    return 1;
}

// Command /refill - untuk admin level 10 reset HBE ke maksimal
CMD:refill(playerid, params[])
{
    if(!PlayerInfo[playerid][pLogged])
    {
        SendClientMessage(playerid, 0xFF0000FF, "ERROR: Anda harus login terlebih dahulu!");
        return 1;
    }

    if(PlayerInfo[playerid][pAdminLevel] < 10)
    {
        SendClientMessage(playerid, 0xFF0000FF, "ERROR: Anda tidak memiliki akses ke command ini! (Admin Level 10 required)");
        return 1;
    }

    new targetid;
    if(sscanf(params, "u", targetid))
    {
        SendClientMessage(playerid, 0xFFFFFFFF, "USAGE: /refill [playerid/name]");
        return 1;
    }

    if(!IsPlayerConnected(targetid))
    {
        SendClientMessage(playerid, 0xFF0000FF, "ERROR: Player tidak online!");
        return 1;
    }

    if(!PlayerInfo[targetid][pLogged])
    {
        SendClientMessage(playerid, 0xFF0000FF, "ERROR: Player belum login!");
        return 1;
    }

    // Reset HBE to maximum
    PlayerInfo[targetid][pHunger] = 100;
    PlayerInfo[targetid][pBladder] = 100;
    PlayerInfo[targetid][pEnergy] = 100;

    // Update progress bars
    UpdateHBEProgressBars(targetid);

    // Messages
    new string[128];
    format(string, sizeof(string), "AdmCmd: Admin %s telah mengisi ulang HBE Anda ke maksimal.", PlayerInfo[playerid][pName]);
    SendClientMessage(targetid, COLOR_ADMIN, string);

    format(string, sizeof(string), "INFO: {FFFFFF}Anda telah mengisi ulang HBE %s ke maksimal.", PlayerInfo[targetid][pName]);
    SendClientMessage(playerid, COLOR_KAKZAH, string);

    // Log admin command
    new logStr[256];
    format(logStr, sizeof(logStr), "ADMIN: %s refilled HBE for %s", PlayerInfo[playerid][pName], PlayerInfo[targetid][pName]);
    print(logStr);

    return 1;
}

// Command /stoppiss - untuk menghentikan kencing
CMD:stoppiss(playerid, params[])
{
    if(!PlayerInfo[playerid][pLogged])
    {
        SendClientMessage(playerid, 0xFF0000FF, "ERROR: Anda harus login terlebih dahulu!");
        return 1;
    }

    if(!PlayerInfo[playerid][pIsPissing])
    {
        SendClientMessage(playerid, 0xFF0000FF, "ERROR: Anda tidak sedang kencing!");
        return 1;
    }

    // Stop pissing
    PlayerInfo[playerid][pIsPissing] = false;
    if(PlayerInfo[playerid][pPissTimer] != 0)
    {
        KillTimer(PlayerInfo[playerid][pPissTimer]);
        PlayerInfo[playerid][pPissTimer] = 0;
    }
    SetPlayerSpecialAction(playerid, SPECIAL_ACTION_NONE);
    ClearAnimations(playerid);

    SendClientMessage(playerid, 0x00FF00FF, "INFO: {FFFFFF}Anda menghentikan kencing. Bladder tidak terisi penuh.");

    return 1;
}



// Command /hudmode - untuk mengubah mode tampilan HBE
CMD:hudmode(playerid, params[])
{
    if(!PlayerInfo[playerid][pLogged])
    {
        SendClientMessage(playerid, 0xFF0000FF, "ERROR: Anda harus login terlebih dahulu!");
        return 1;
    }

    new dialogStr[128];
    format(dialogStr, sizeof(dialogStr),
        "HBE Mode: {FFFF00}%s",
        (PlayerInfo[playerid][pHBEMode] == 0) ? "Classic" : "Simple");

    Dialog_Show(playerid, HUDModeDialog, DIALOG_STYLE_LIST, "HudMode Style", dialogStr, "Select", "Cancel");

    return 1;
}

// Dialog untuk HUD Mode
Dialog:HUDModeDialog(playerid, response, listitem, inputtext[])
{
    if(!response) return 1; // Exit clicked, close dialog

    // Hanya ada satu pilihan (listitem 0) yang akan toggle mode
    if(listitem == 0)
    {
        // Toggle mode (bergantian antara Classic dan Simple)
        if(PlayerInfo[playerid][pHBEMode] == 0)
        {
            PlayerInfo[playerid][pHBEMode] = 1; // Change to Simple
            SendClientMessage(playerid, COLOR_KAKZAH, "INFO: {FFFFFF}HBE mode diubah ke {FFFF00}Simple Mode{FFFFFF}.");
        }
        else
        {
            PlayerInfo[playerid][pHBEMode] = 0; // Change to Classic
            SendClientMessage(playerid, COLOR_KAKZAH, "INFO: {FFFFFF}HBE mode diubah ke {FFFF00}Classic Mode{FFFFFF}.");
        }

        // Recreate textdraws with new mode
        DestroyHBETextDraw(playerid);
        CreateHBETextDraw(playerid);

        // Save to database
        SavePlayerData(playerid);

        // Show dialog again with updated mode
        new dialogStr[128];
        format(dialogStr, sizeof(dialogStr),
            "HBE Mode: {FFFF00}%s",
            (PlayerInfo[playerid][pHBEMode] == 0) ? "Classic" : "Simple");

        Dialog_Show(playerid, HUDModeDialog, DIALOG_STYLE_LIST, "HudMode Style", dialogStr, "Select", "Exit");
    }

    return 1;
}