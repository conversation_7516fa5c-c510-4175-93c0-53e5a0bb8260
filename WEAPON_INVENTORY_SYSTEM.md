# Weapon & Inventory System Documentation

## Overview
Sistem ini terdiri dari dua bagian utama:
1. **Player Weapon System** - Menyimpan senjata player di database `player_weapon`
2. **Player Inventory System** - Menyimpan item-item lain di database `player_inventory`

## Database Tables

### 1. Table `player_weapon`
```sql
CREATE TABLE IF NOT EXISTS `player_weapon` (
    `id` int(11) NOT NULL AUTO_INCREMENT,
    `player_id` int(11) NOT NULL,
    `weapon_slot` int(2) NOT NULL,
    `weapon_id` int(3) NOT NULL,
    `ammo` int(11) NOT NULL DEFAULT 0,
    `magazine_size` int(11) NOT NULL DEFAULT 0,
    `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
    `updated_at` timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (`id`),
    UNIQUE KEY `player_weapon_slot` (`player_id`, `weapon_slot`),
    KEY `player_id` (`player_id`)
) ENGINE=MyISAM DEFAULT CHARSET=latin1;
```

### 2. Table `player_inventory`
```sql
CREATE TABLE IF NOT EXISTS `player_inventory` (
    `id` int(11) NOT NULL AUTO_INCREMENT,
    `player_id` int(11) NOT NULL,
    `item_type` varchar(32) NOT NULL,
    `item_name` varchar(64) NOT NULL,
    `quantity` int(11) NOT NULL DEFAULT 1,
    `item_data` varchar(128) DEFAULT '',
    `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
    `updated_at` timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (`id`),
    KEY `player_id` (`player_id`),
    KEY `item_type` (`item_type`)
) ENGINE=MyISAM DEFAULT CHARSET=latin1;
```

## Functions

### Weapon Functions
- `SavePlayerWeapons(playerid)` - Menyimpan semua senjata player ke database
- `LoadPlayerWeapons(playerid)` - Memuat senjata player dari database
- `GivePlayerCustomWeapon(playerid, weaponid, ammo)` - Memberikan senjata dan otomatis menyimpan ke database

### Inventory Functions
- `AddPlayerInventoryItem(playerid, itemType[], itemName[], quantity, itemData[])` - Menambah item ke inventory
- `RemovePlayerInventoryItem(playerid, itemType[], itemName[], quantity)` - Menghapus item dari inventory
- `GetPlayerInventoryItemCount(playerid, itemType[], itemName[])` - Mengecek jumlah item

## Commands

### Admin Commands (Level 10)
- `/agiveitem [playerid] [item_type] [item_name] [quantity] [item_data(optional)]`
  - Memberikan item kepada player
  - Contoh: `/agiveitem 0 component Silencer 1`
  - Contoh: `/agiveitem 0 bait Worm 10`

### Admin Commands (Level 5)
- `/acheckitem [playerid]`
  - Melihat inventory player lain

### Player Commands
- `/inventory`
  - Melihat inventory sendiri (termasuk senjata)
- `/useitem [item_type] [item_name]`
  - Menggunakan item dari inventory
  - Contoh: `/useitem component Silencer`
  - Contoh: `/useitem bait Worm`

## Item Types

### 1. Component
- **Silencer** - Dapat dipasang pada pistol (weapon ID 22, 23, 24)

### 2. Bait (Umpan)
- **Worm** - Umpan cacing untuk memancing
- **Corn** - Umpan jagung untuk memancing

### 3. Food
- **Burger** - Menambah 15 HP
- **Pizza** - Menambah 20 HP
- **Default food** - Menambah 10 HP

## How It Works

### Weapon System
1. Saat player login, `LoadPlayerWeapons()` dipanggil untuk memuat senjata dari database
2. Saat player mendapat senjata baru via `GivePlayerCustomWeapon()`, otomatis tersimpan ke database
3. Saat player disconnect/save, `SavePlayerWeapons()` dipanggil untuk menyimpan ke database

### Inventory System
1. Admin dapat memberikan item menggunakan `/agiveitem`
2. Item tersimpan di database dengan sistem quantity (jumlah)
3. Player dapat melihat inventory dengan `/inventory`
4. Player dapat menggunakan item dengan `/useitem`
5. Saat item digunakan, quantity berkurang atau item dihapus jika habis

## Integration Points

### Automatic Saving
- Weapons disimpan otomatis saat:
  - Player login (load)
  - Player disconnect (save)
  - Player mendapat senjata baru
  - Auto-save setiap 5 menit

### Database Integration
- Menggunakan MySQL dengan prepared statements
- Menggunakan cache system untuk query results
- Error handling untuk database operations

## Example Usage

```pawn
// Admin memberikan silencer kepada player ID 0
/agiveitem 0 component Silencer 1

// Admin memberikan umpan cacing kepada player ID 0
/agiveitem 0 bait Worm 10

// Player menggunakan silencer
/useitem component Silencer

// Player melihat inventory
/inventory
```

## Notes
- Sistem ini terintegrasi dengan custom weapon system yang sudah ada
- Weapon ammo dan magazine size tersimpan terpisah dari inventory items
- Item data field dapat digunakan untuk menyimpan informasi tambahan tentang item
- Sistem menggunakan unique constraint untuk weapon slots (1 weapon per slot per player)
